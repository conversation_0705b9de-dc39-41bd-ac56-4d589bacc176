{"designSystem": {"name": "iBanko Dashboard Design System", "version": "1.0.0", "description": "设计系统配置文件，基于iBanko仪表盘UI和Ant Design组件。", "overallStyle": {"fontFamily": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "baseFontSize": "14px", "lineHeight": 1.5715, "borderRadius": "8px", "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"}, "colors": {"primary": "#1890ff", "secondary": "#52c41a", "info": "#1890ff", "success": "#52c41a", "warning": "#faad14", "error": "#f5222d", "textBase": "#000000d9", "textSecondary": "#********", "disabled": "#********", "border": "#d9d9d9", "divider": "#f0f0f0", "backgroundLight": "#f0f2f5", "backgroundDark": "#1a1a1a", "componentBackground": "#ffffff", "darkComponentBackground": "#2a2a2a", "headingColor": "#000000d9", "darkHeadingColor": "#ffffffd9"}, "breakpoints": {"xs": "480px", "sm": "576px", "md": "768px", "lg": "992px", "xl": "1200px", "xxl": "1600px"}}, "structuralElements": {"layout": {"headerHeight": "64px", "siderWidth": "220px", "siderCollapsedWidth": "80px", "contentPadding": "24px"}, "typography": {"headings": {"h1": {"fontSize": "38px", "fontWeight": 600}, "h2": {"fontSize": "30px", "fontWeight": 600}, "h3": {"fontSize": "24px", "fontWeight": 600}, "h4": {"fontSize": "20px", "fontWeight": 600}, "h5": {"fontSize": "16px", "fontWeight": 600}}, "paragraph": {"marginBottom": "1em"}}, "buttons": {"baseHeight": "32px", "borderRadius": "6px", "primaryBg": "@primary-color", "primaryColor": "#fff", "defaultBg": "#fff", "defaultBorder": "@border-color-base", "defaultColor": "@text-color", "padding": "0 15px"}, "inputs": {"baseHeight": "32px", "borderRadius": "6px", "borderColor": "@border-color-base", "hoverBorderColor": "@primary-color", "focusBorderColor": "@primary-color", "placeholderColor": "#bfbfbf", "padding": "4px 11px"}, "cards": {"backgroundColor": "@component-background", "darkBackgroundColor": "@dark-component-background", "borderColor": "transparent", "borderRadius": "@border-radius-base", "padding": "24px", "headerPadding": "16px 24px"}, "tables": {"headerBg": "#fafafa", "rowHoverBg": "#f5f5f5", "padding": "16px", "borderColor": "@divider-color"}, "charts": {"lineColor": "@primary-color", "fillColor": "@primary-color-light", "tooltipBg": "rgba(0,0,0,0.75)", "tooltipColor": "#fff"}}, "layoutPrinciples": {"spacingSystem": {"xxs": "4px", "xs": "8px", "sm": "12px", "md": "16px", "lg": "24px", "xl": "32px", "xxl": "48px"}, "gridSystem": {"columns": 24, "gutter": "16px"}, "responsiveDesign": "使用Ant Design的响应式断点和栅格系统进行布局。", "flexibility": "所有组件和布局应具有良好的弹性，以适应不同内容长度和屏幕尺寸。", "consistency": "保持边距、填充、圆角和字体大小在整个应用中的一致性。"}, "antDesignIntegration": {"themeVariables": {"@primary-color": "根据主题调整（例如：亮模式#1890ff，暗模式#52c41a）", "@border-radius-base": "8px", "@font-family": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "@card-padding-base": "24px", "@layout-header-height": "64px", "@layout-sider-width": "220px", "@layout-sider-collapsed-width": "80px", "@layout-body-background": "根据主题调整", "@component-background": "根据主题调整", "@text-color": "根据主题调整", "@heading-color": "根据主题调整"}, "componentUsage": {"Layout": "用于整体页面布局，包括Sider、Header、Content", "Card": "用于信息展示的卡片，如“My Card”、“Financial Record”", "Button": "用于操作按钮，如“Deposit”、“Withdraw”", "Table": "用于列表展示，如“Transactions”", "Input": "用于输入框，如搜索框", "Typography": "用于文本展示，包括标题和段落", "Menu": "用于左侧导航栏", "Avatar": "用于用户头像", "Progress": "用于进度条，如“Money Flow”图表中的百分比", "Dropdown": "用于筛选器，如“Month”下拉菜单", "Charts": "使用Ant Design Charts或类似库（如AntV G2Plot）来渲染图表，例如“Money Flow”折线图。"}}, "darkMode": {"enabled": true, "toggleMechanism": "通过Ant Design的主题切换功能实现，动态调整CSS变量或Ant Design主题配置。", "colors": {"background": "#121212", "componentBackground": "#2a2a2a", "textBase": "#ffffffd9", "textSecondary": "#ffffffa6", "border": "#424242", "primary": "#52c41a"}}}}