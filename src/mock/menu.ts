/**
 * 菜单数据模拟
 * 用于测试菜单渲染功能
 */

import type { MenuRecord } from '@/types/auth';
import { MenuType } from '@/types/auth';

/**
 * 模拟菜单数据
 */
export const mockMenuData: MenuRecord[] = [
  {
    id: 1,
    parentId: 0,
    name: 'dashboard',
    title: '工作台',
    type: MenuType.MENU,
    path: '/dashboard',
    component: 'Dashboard',
    icon: 'dashboard',
    children: []
  },
  {
    id: 2,
    parentId: 0,
    name: 'customer',
    title: '客户管理',
    type: MenuType.DIRECTORY,
    path: '/customer',
    component: '',
    icon: 'user',
    children: [
      {
        id: 21,
        parentId: 2,
        name: 'customer-list',
        title: '客户列表',
        type: MenuType.MENU,
        path: '/customer/list',
        component: 'CustomerList',
        icon: 'team',
        children: []
      },
      {
        id: 22,
        parentId: 2,
        name: 'customer-profile',
        title: '客户档案',
        type: MenuType.MENU,
        path: '/customer/profile',
        component: 'CustomerProfile',
        icon: 'idcard',
        children: []
      }
    ]
  },
  {
    id: 3,
    parentId: 0,
    name: 'appointment',
    title: '预约管理',
    type: MenuType.DIRECTORY,
    path: '/appointment',
    component: '',
    icon: 'calendar',
    children: [
      {
        id: 31,
        parentId: 3,
        name: 'appointment-list',
        title: '预约列表',
        type: MenuType.MENU,
        path: '/appointment/list',
        component: 'AppointmentList',
        icon: 'schedule',
        children: []
      },
      {
        id: 32,
        parentId: 3,
        name: 'appointment-calendar',
        title: '预约日历',
        type: MenuType.MENU,
        path: '/appointment/calendar',
        component: 'AppointmentCalendar',
        icon: 'calendar',
        children: []
      }
    ]
  },
  {
    id: 4,
    parentId: 0,
    name: 'financial',
    title: '财务管理',
    type: MenuType.DIRECTORY,
    path: '/financial',
    component: '',
    icon: 'wallet',
    children: [
      {
        id: 41,
        parentId: 4,
        name: 'transaction',
        title: '交易记录',
        type: MenuType.MENU,
        path: '/financial/transaction',
        component: 'Transaction',
        icon: 'transaction',
        children: []
      },
      {
        id: 42,
        parentId: 4,
        name: 'financial-report',
        title: '财务报表',
        type: MenuType.MENU,
        path: '/financial/report',
        component: 'FinancialReport',
        icon: 'bar-chart',
        children: []
      },
      {
        id: 43,
        parentId: 4,
        name: 'payment',
        title: '收款管理',
        type: MenuType.MENU,
        path: '/financial/payment',
        component: 'Payment',
        icon: 'credit-card',
        children: []
      }
    ]
  },
  {
    id: 5,
    parentId: 0,
    name: 'inventory',
    title: '库存管理',
    type: MenuType.DIRECTORY,
    path: '/inventory',
    component: '',
    icon: 'database',
    children: [
      {
        id: 51,
        parentId: 5,
        name: 'product',
        title: '产品管理',
        type: MenuType.MENU,
        path: '/inventory/product',
        component: 'Product',
        icon: 'shopping',
        children: []
      },
      {
        id: 52,
        parentId: 5,
        name: 'stock',
        title: '库存统计',
        type: MenuType.MENU,
        path: '/inventory/stock',
        component: 'Stock',
        icon: 'stock',
        children: []
      }
    ]
  },
  {
    id: 6,
    parentId: 0,
    name: 'analytics',
    title: '数据分析',
    type: MenuType.MENU,
    path: '/analytics',
    component: 'Analytics',
    icon: 'line-chart',
    children: []
  },
  {
    id: 7,
    parentId: 0,
    name: 'system',
    title: '系统管理',
    type: MenuType.DIRECTORY,
    path: '/system',
    component: '',
    icon: 'setting',
    children: [
      {
        id: 71,
        parentId: 7,
        name: 'user-management',
        title: '用户管理',
        type: MenuType.MENU,
        path: '/system/user',
        component: 'UserManagement',
        icon: 'user',
        children: []
      },
      {
        id: 72,
        parentId: 7,
        name: 'role-management',
        title: '角色管理',
        type: MenuType.MENU,
        path: '/system/role',
        component: 'RoleManagement',
        icon: 'team',
        children: []
      },
      {
        id: 73,
        parentId: 7,
        name: 'menu-management',
        title: '菜单管理',
        type: MenuType.MENU,
        path: '/system/menu',
        component: 'MenuManagement',
        icon: 'menu',
        children: []
      },
      {
        id: 74,
        parentId: 7,
        name: 'system-config',
        title: '系统配置',
        type: MenuType.MENU,
        path: '/system/config',
        component: 'SystemConfig',
        icon: 'setting',
        children: []
      }
    ]
  }
];

/**
 * 获取模拟菜单数据
 * @returns 菜单数据
 */
export const getMockMenuData = (): MenuRecord[] => {
  return JSON.parse(JSON.stringify(mockMenuData));
};
