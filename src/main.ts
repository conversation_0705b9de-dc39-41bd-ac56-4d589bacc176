import { createApp } from 'vue'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './style.css'
import './theme/global.css'
import './theme/autofill-override.css'
import App from './App.vue'
import { setupRouter } from './router/index'
import pinia from './stores'
const app = createApp(App)

// 安装 Pinia 状态管理
app.use(pinia)

// 安装 Ant Design Vue
app.use(Antd)

// 安装路由
setupRouter(app)

// 开发环境下加载持久化测试工具
if (import.meta.env.DEV) {
  import('@/utils/persistTest')
}

app.mount('#app')
