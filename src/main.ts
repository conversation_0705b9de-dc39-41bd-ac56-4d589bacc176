import { createApp } from 'vue'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import './style.css'
import './theme/global.css'
import './theme/autofill-override.css'
import App from './App.vue'
import router, { setupRouter } from './router/index'
import pinia from './stores'

const app = createApp(App)

// 安装 Pinia 状态管理
app.use(pinia)

// 安装 Ant Design Vue
app.use(Antd)

// 安装路由
setupRouter(app)

app.mount('#app')
