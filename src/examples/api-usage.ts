/**
 * API使用示例
 * 展示如何使用封装的axios和各种API服务
 */

import { 
  AuthApi, 
  UserApi, 
  CustomerApi, 
  RoleApi, 
  MenuApi,
  TokenManager,
  request 
} from '@/api';
import type { 
  UserLoginReq,
  UserQueryParams,
  CustomerQueryParams,
  RoleQueryParams,
  MenuQueryParams 
} from '@/api';

/**
 * 认证相关示例
 */
export class AuthExamples {
  /**
   * 用户登录示例
   */
  static async loginExample() {
    try {
      const loginData: UserLoginReq = {
        username: 'admin',
        password: '123456'
      };
      
      const authRecord = await AuthApi.login(loginData);
      
      // 保存token
      TokenManager.setToken(authRecord.authInfo.accessToken);
      
      console.log('登录成功:', authRecord);
      console.log('用户信息:', authRecord.userInfo);
      console.log('菜单权限:', authRecord.authInfo.menus);
      
      return authRecord;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }
  
  /**
   * 用户登出示例
   */
  static async logoutExample() {
    try {
      await AuthApi.logout();
      
      // 清除token
      TokenManager.removeToken();
      
      console.log('登出成功');
    } catch (error) {
      console.error('登出失败:', error);
      // 即使失败也要清除本地token
      TokenManager.removeToken();
    }
  }
  
  /**
   * 获取当前用户信息示例
   */
  static async getCurrentUserExample() {
    try {
      const userInfo = await AuthApi.getCurrentUser();
      console.log('当前用户信息:', userInfo);
      return userInfo;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }
}

/**
 * 用户管理示例
 */
export class UserExamples {
  /**
   * 分页查询用户示例
   */
  static async getUserPageExample() {
    try {
      const params: UserQueryParams = {
        page: 1,
        pageSize: 10,
        username: 'admin',
        status: 1
      };
      
      const pageResult = await UserApi.getUserPage(params);
      console.log('用户分页数据:', pageResult);
      
      return pageResult;
    } catch (error) {
      console.error('查询用户失败:', error);
      throw error;
    }
  }
  
  /**
   * 创建用户示例
   */
  static async createUserExample() {
    try {
      const userData = {
        username: 'newuser',
        nickname: '新用户',
        password: '123456',
        phone: '13800138000',
        email: '<EMAIL>',
        gender: 1,
        status: 1,
        roleIds: [1, 2]
      };
      
      const newUser = await UserApi.createUser(userData);
      console.log('创建用户成功:', newUser);
      
      return newUser;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }
}

/**
 * 客户管理示例
 */
export class CustomerExamples {
  /**
   * 分页查询客户示例
   */
  static async getCustomerPageExample() {
    try {
      const params: CustomerQueryParams = {
        page: 1,
        pageSize: 20,
        name: '张',
        status: 1,
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      };
      
      const pageResult = await CustomerApi.getCustomerPage(params);
      console.log('客户分页数据:', pageResult);
      
      return pageResult;
    } catch (error) {
      console.error('查询客户失败:', error);
      throw error;
    }
  }
  
  /**
   * 导出客户数据示例
   */
  static async exportCustomersExample() {
    try {
      const params: CustomerQueryParams = {
        status: 1
      };
      
      const blob = await CustomerApi.exportCustomers(params);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `customers_${new Date().getTime()}.xlsx`;
      link.click();
      
      // 清理URL对象
      window.URL.revokeObjectURL(url);
      
      console.log('客户数据导出成功');
    } catch (error) {
      console.error('导出客户数据失败:', error);
      throw error;
    }
  }
}

/**
 * 角色管理示例
 */
export class RoleExamples {
  /**
   * 获取角色列表示例
   */
  static async getRoleListExample() {
    try {
      const roleList = await RoleApi.getRoleList();
      console.log('角色列表:', roleList);
      
      return roleList;
    } catch (error) {
      console.error('获取角色列表失败:', error);
      throw error;
    }
  }
  
  /**
   * 设置角色权限示例
   */
  static async setRoleMenusExample() {
    try {
      const roleId = 1;
      const menuIds = [1, 2, 3, 4, 5];
      
      await RoleApi.setRoleMenus(roleId, menuIds);
      console.log('角色权限设置成功');
    } catch (error) {
      console.error('设置角色权限失败:', error);
      throw error;
    }
  }
}

/**
 * 菜单管理示例
 */
export class MenuExamples {
  /**
   * 获取树形菜单示例
   */
  static async getMenuTreeExample() {
    try {
      const menuTree = await MenuApi.getMenuTree();
      console.log('树形菜单:', menuTree);
      
      return menuTree;
    } catch (error) {
      console.error('获取菜单树失败:', error);
      throw error;
    }
  }
}

/**
 * 自定义请求示例
 */
export class CustomRequestExamples {
  /**
   * 自定义GET请求示例
   */
  static async customGetExample() {
    try {
      const response = await request.get('/custom/endpoint', {
        param1: 'value1',
        param2: 'value2'
      }, {
        loading: true,
        errorMessage: true,
        successMessage: false
      });
      
      console.log('自定义GET请求成功:', response);
      return response;
    } catch (error) {
      console.error('自定义GET请求失败:', error);
      throw error;
    }
  }
  
  /**
   * 文件上传示例
   */
  static async uploadFileExample(file: File) {
    try {
      const response = await request.upload('/upload', file, {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '文件上传成功'
      });
      
      console.log('文件上传成功:', response);
      return response;
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  }
  
  /**
   * 无token请求示例
   */
  static async noTokenRequestExample() {
    try {
      const response = await request.get('/public/data', {}, {
        withToken: false,
        loading: false,
        errorMessage: true
      });
      
      console.log('无token请求成功:', response);
      return response;
    } catch (error) {
      console.error('无token请求失败:', error);
      throw error;
    }
  }
}

/**
 * Token管理示例
 */
export class TokenExamples {
  /**
   * Token操作示例
   */
  static tokenOperationExample() {
    // 检查是否有token
    const hasToken = TokenManager.hasToken();
    console.log('是否有token:', hasToken);
    
    // 获取token
    const token = TokenManager.getToken();
    console.log('当前token:', token);
    
    // 设置token
    TokenManager.setToken('new-token-value');
    console.log('设置新token成功');
    
    // 清除token
    TokenManager.removeToken();
    console.log('清除token成功');
  }
}
