<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      class="main-sider"
    >
      <div class="logo">
        <h3 v-if="!collapsed">医美CRM</h3>
        <h3 v-else>CRM</h3>
      </div>
      
      <!-- 菜单占位符，后续会根据动态路由生成 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        class="main-menu"
      >
        <a-menu-item key="dashboard">
          <template #icon>
            <DashboardOutlined />
          </template>
          工作台
        </a-menu-item>
      </a-menu>
    </a-layout-sider>

    <!-- 主内容区域 -->
    <a-layout class="main-content-layout">
      <!-- 顶部导航栏 -->
      <a-layout-header class="main-header">
        <div class="header-left">
          <a-button
            type="text"
            class="trigger"
            @click="toggleCollapsed"
          >
            <MenuUnfoldOutlined v-if="collapsed" />
            <MenuFoldOutlined v-else />
          </a-button>
          
          <!-- 面包屑 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item>
              <HomeOutlined />
            </a-breadcrumb-item>
            <a-breadcrumb-item>工作台</a-breadcrumb-item>
          </a-breadcrumb>
        </div>

        <div class="header-right">
          <!-- 用户信息下拉菜单 -->
          <a-dropdown>
            <a-space class="user-info">
              <a-avatar size="small">
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <span>管理员</span>
              <DownOutlined />
            </a-space>
            
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 页面内容 -->
      <a-layout-content class="main-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>

      <!-- 底部 -->
      <a-layout-footer class="main-footer">
        医美CRM管理系统 ©2024 Created by Your Company
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  DashboardOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  HomeOutlined,
  UserOutlined,
  DownOutlined,
  LogoutOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ROUTE_PATHS } from '@/constants/routes'

const router = useRouter()

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref(['dashboard'])
const openKeys = ref<string[]>([])

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

// 处理退出登录
const handleLogout = () => {
  // 清除本地存储的用户信息
  localStorage.removeItem('access_token')
  localStorage.removeItem('user_role')
  localStorage.removeItem('user_permissions')
  
  message.success('退出登录成功')
  
  // 跳转到登录页
  router.push(ROUTE_PATHS.LOGIN)
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
}

.main-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 6px;
}

.logo h3 {
  color: white;
  margin: 0;
  font-weight: 600;
}

.main-menu {
  border-right: none;
}

.main-content-layout {
  margin-left: 200px;
  transition: margin-left 0.2s;
}

.main-layout :deep(.ant-layout-sider-collapsed) + .main-content-layout {
  margin-left: 80px;
}

.main-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.breadcrumb {
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  cursor: pointer;
  padding: 0 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

.main-content {
  margin: 24px;
  padding: 0;
  min-height: calc(100vh - 64px - 70px - 48px);
}

.content-wrapper {
  background: #fff;
  padding: 24px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 100%;
}

.main-footer {
  text-align: center;
  color: #666;
  background: #f0f2f5;
}
</style>
