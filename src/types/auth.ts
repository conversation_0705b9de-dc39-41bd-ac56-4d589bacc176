/**
 * 认证相关类型定义
 */

import type { ApiResponse, ID } from './api';

// 用户登录请求
export interface UserLoginReq {
  username: string;
  password: string;
}

// 角色信息
export interface RoleRecord {
  id: ID;
  name: string;
  key: string;
}

// 菜单信息
export interface MenuRecord {
  id: ID;
  parentId: ID;
  name: string;
  title: string;
  type: number; // 1-菜单 2-按钮 3-目录
  path: string;
  component: string;
  icon: string;
  children: MenuRecord[];
}

// 菜单类型常量
export const MenuType = {
  MENU: 1,      // 菜单
  BUTTON: 2,    // 按钮
  DIRECTORY: 3, // 目录
} as const;

// 用户信息
export interface UserLoginRecord {
  id: ID;
  username: string;
  nickname: string;
  avatar: string;
  phone: string;
  email: string;
  gender: number;
  status: number;
  roles: RoleRecord[];
}

// 授权信息
export interface Auth {
  accessToken: string;
  menus: MenuRecord[];
}

// 认证记录
export interface AuthRecord {
  userInfo: UserLoginRecord;
  authInfo: Auth;
}

// 登录响应
export type LoginResponse = ApiResponse<AuthRecord>;

// 修改密码请求
export interface ChangePasswordReq {
  oldPassword: string;
  newPassword: string;
}

// Token验证响应
export interface TokenValidateResponse {
  valid: boolean;
  expiresAt?: string;
}

// 权限检查参数
export interface PermissionCheckParams {
  permission?: string;
  role?: string;
  menu?: string;
}

// 用户权限信息
export interface UserPermissions {
  roles: string[];
  permissions: string[];
  menus: string[];
}

// 登录状态
export const LoginStatus = {
  NOT_LOGGED_IN: 'not_logged_in',
  LOGGED_IN: 'logged_in',
  TOKEN_EXPIRED: 'token_expired',
  LOGGING_IN: 'logging_in',
  LOGGING_OUT: 'logging_out',
} as const;

export type LoginStatusType = typeof LoginStatus[keyof typeof LoginStatus];
