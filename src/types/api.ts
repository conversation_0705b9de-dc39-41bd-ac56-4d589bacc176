/**
 * 基础API类型定义
 * 只包含通用的、跨模块的基础类型
 */

// 基础响应结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 分页请求参数
export interface PageParams {
  page?: number;
  pageSize?: number;
  [key: string]: any;
}

// 分页响应数据
export interface PageResult<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

// 请求配置扩展
export interface RequestConfig {
  /** 是否显示loading */
  loading?: boolean;
  /** 是否显示错误提示 */
  errorMessage?: boolean;
  /** 是否显示成功提示 */
  successMessage?: boolean;
  /** 自定义成功提示文本 */
  successText?: string;
  /** 是否需要token */
  withToken?: boolean;
  /** 重试次数 */
  retry?: number;
  /** 请求超时时间 */
  timeout?: number;
}

// HTTP状态码常量
export const HttpStatus = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
} as const;

// 业务状态码常量
export const BusinessCode = {
  SUCCESS: 0,
  FAIL: 1,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500,
} as const;

// 错误类型
export interface ApiError {
  code: number;
  message: string;
  data?: any;
  stack?: string;
}

// 通用ID类型
export type ID = number | string;

// 通用状态常量
export const CommonStatus = {
  DISABLED: 0,
  ENABLED: 1,
} as const;

// 通用性别常量
export const Gender = {
  UNKNOWN: 0,
  MALE: 1,
  FEMALE: 2,
} as const;

// 通用排序参数
export interface SortParams {
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

// 通用时间范围参数
export interface TimeRangeParams {
  startDate?: string;
  endDate?: string;
}

// 通用搜索参数
export interface SearchParams {
  keyword?: string;
  [key: string]: any;
}

// 文件上传响应
export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
  type: string;
}

// 批量操作参数
export interface BatchOperationParams {
  ids: ID[];
  [key: string]: any;
}

// 树形节点基础接口
export interface TreeNode<T = any> {
  id: ID;
  parentId?: ID;
  children?: TreeNode<T>[];
  [key: string]: any;
}
