import type { RouteRecordRaw, RouteMeta } from 'vue-router'
import type { Component } from 'vue'

/**
 * 菜单项类型定义
 * 用于动态路由生成和菜单渲染
 */
export interface MenuItem {
  /** 路由路径 */
  path: string
  
  /** 路由名称 */
  name: string
  
  /** 页面标题 */
  title: string
  
  /** 菜单图标 */
  icon?: string
  
  /** 组件路径或组件对象 */
  component?: string | Component
  
  /** 是否在菜单中隐藏 */
  hideInMenu?: boolean
  
  /** 是否需要认证 */
  requireAuth?: boolean
  
  /** 是否需要管理员权限 */
  requireAdmin?: boolean
  
  /** 是否保持组件活跃状态 */
  keepAlive?: boolean
  
  /** 排序权重 */
  sort?: number
  
  /** 子菜单项 */
  children?: MenuItem[]
  
  /** 路由重定向 */
  redirect?: string
  
  /** 面包屑路径 */
  breadcrumb?: string[]
  
  /** 扩展的路由元信息 */
  meta?: Record<string, any>
}

/**
 * 扩展的路由记录类型
 * 继承Vue Router的RouteRecordRaw，添加自定义属性
 */
export interface ExtendedRouteRecordRaw extends Omit<RouteRecordRaw, 'meta' | 'children'> {
  /** 扩展的元信息 */
  meta?: ExtendedRouteMeta
  
  /** 子路由 */
  children?: ExtendedRouteRecordRaw[]
}

/**
 * 扩展的路由元信息类型
 */
export interface ExtendedRouteMeta extends RouteMeta {
  /** 页面标题 */
  title?: string
  
  /** 菜单图标 */
  icon?: string
  
  /** 是否在菜单中隐藏 */
  hideInMenu?: boolean
  
  /** 是否需要认证 */
  requireAuth?: boolean
  
  /** 是否需要管理员权限 */
  requireAdmin?: boolean
  
  /** 是否保持组件活跃状态 */
  keepAlive?: boolean
  
  /** 排序权重 */
  sort?: number
  
  /** 面包屑路径 */
  breadcrumb?: string[]
  
  /** 权限代码列表 */
  permissions?: string[]
  
  /** 角色列表 */
  roles?: string[]
}

/**
 * 路由配置选项
 */
export interface RouterOptions {
  /** 基础路径 */
  base?: string
  
  /** 路由模式 */
  mode?: 'hash' | 'history'
  
  /** 是否启用严格模式 */
  strict?: boolean
  
  /** 滚动行为 */
  scrollBehavior?: boolean
}

/**
 * 动态路由生成函数的参数类型
 */
export interface GenerateRoutesParams {
  /** 菜单项列表 */
  menuItems: MenuItem[]
  
  /** 父级路径 */
  parentPath?: string
  
  /** 是否为懒加载 */
  lazy?: boolean
}

/**
 * 路由守卫上下文类型
 */
export interface RouteGuardContext {
  /** 用户信息 */
  user?: any
  
  /** 权限列表 */
  permissions?: string[]
  
  /** 角色列表 */
  roles?: string[]
  
  /** 是否已认证 */
  isAuthenticated?: boolean
}

/**
 * 面包屑项类型
 */
export interface BreadcrumbItem {
  /** 标题 */
  title: string
  
  /** 路径 */
  path?: string
  
  /** 是否为链接 */
  link?: boolean
}

/**
 * 菜单树节点类型
 */
export interface MenuTreeNode extends MenuItem {
  /** 节点ID */
  id: string
  
  /** 父节点ID */
  parentId?: string
  
  /** 节点层级 */
  level: number
  
  /** 是否展开 */
  expanded?: boolean
  
  /** 是否选中 */
  selected?: boolean
  
  /** 子节点 */
  children?: MenuTreeNode[]
}
