/**
 * 角色管理相关类型定义
 */

import type { PageParams, ID } from './api';
import type { RoleRecord } from './auth';

// 角色查询参数
export interface RoleQueryParams extends PageParams {
  name?: string;
  key?: string;
  status?: number;
  startDate?: string;
  endDate?: string;
}

// 角色表单数据
export interface RoleFormData {
  name: string;
  key: string;
  description?: string;
  status?: number;
  menuIds?: ID[]; // 关联的菜单ID
  permissions?: string[]; // 权限标识
  sort?: number;
  remark?: string;
}

// 扩展角色信息
export interface RoleDetailRecord extends RoleRecord {
  description?: string;
  status: number;
  menuIds: ID[];
  permissions: string[];
  sort: number;
  remark?: string;
  userCount?: number; // 关联用户数量
  createTime: string;
  updateTime: string;
  createBy?: string;
  updateBy?: string;
}

// 角色状态常量
export const RoleStatus = {
  DISABLED: 0,
  ENABLED: 1,
} as const;

// 角色类型常量
export const RoleType = {
  SYSTEM: 'system',     // 系统角色
  CUSTOM: 'custom',     // 自定义角色
  TEMPORARY: 'temp',    // 临时角色
} as const;

// 角色权限设置参数
export interface RolePermissionParams {
  menuIds: ID[];
  permissions?: string[];
  halfCheckedKeys?: ID[]; // 半选中的菜单ID（父节点部分选中）
}

// 角色复制参数
export interface RoleCopyParams {
  name: string;
  key: string;
  description?: string;
  copyPermissions?: boolean; // 是否复制权限
}

// 角色状态切换参数
export interface RoleStatusParams {
  status: number;
  reason?: string;
}

// 角色统计信息
export interface RoleStatsInfo {
  total: number;
  enabled: number;
  disabled: number;
  systemRoles: number;
  customRoles: number;
}

// 角色权限树节点
export interface RolePermissionTreeNode {
  id: ID;
  parentId?: ID;
  title: string;
  key: string;
  type: number; // 1-菜单 2-按钮 3-目录
  permission?: string;
  children?: RolePermissionTreeNode[];
  disabled?: boolean;
  checkable?: boolean;
}

// 角色用户关联信息
export interface RoleUserInfo {
  id: ID;
  username: string;
  nickname: string;
  avatar?: string;
  status: number;
  assignTime: string;
  assignBy?: string;
}

// 角色分配用户参数
export interface RoleAssignUserParams {
  userIds: ID[];
  replace?: boolean; // 是否替换现有用户
}

// 角色权限检查结果
export interface RolePermissionCheck {
  hasPermission: boolean;
  permissions: string[];
  menus: ID[];
  buttons: ID[];
}

// 角色操作日志
export interface RoleOperationLog {
  id: ID;
  roleId: ID;
  roleName: string;
  operation: string;
  operationData?: any;
  operatorId: ID;
  operatorName: string;
  ip: string;
  createTime: string;
}

// 角色操作类型
export const RoleOperationType = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  COPY: 'copy',
  ASSIGN_PERMISSION: 'assign_permission',
  ASSIGN_USER: 'assign_user',
  REMOVE_USER: 'remove_user',
  CHANGE_STATUS: 'change_status',
} as const;

// 预定义系统角色
export const SystemRoles = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest',
} as const;

// 角色权限级别
export const RolePermissionLevel = {
  READ: 1,      // 只读
  WRITE: 2,     // 读写
  DELETE: 3,    // 删除
  ADMIN: 4,     // 管理
} as const;

export type RoleStatusValue = typeof RoleStatus[keyof typeof RoleStatus];
export type RoleTypeValue = typeof RoleType[keyof typeof RoleType];
export type RoleOperationTypeValue = typeof RoleOperationType[keyof typeof RoleOperationType];
export type SystemRoleValue = typeof SystemRoles[keyof typeof SystemRoles];
