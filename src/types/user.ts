/**
 * 用户管理相关类型定义
 */

import type { PageParams, ID } from './api';
import type { RoleRecord } from './auth';

// 用户查询参数
export interface UserQueryParams extends PageParams {
  username?: string;
  nickname?: string;
  status?: number;
  gender?: number;
  phone?: string;
  email?: string;
  roleId?: ID;
  startDate?: string;
  endDate?: string;
}

// 用户创建/更新参数
export interface UserFormData {
  username: string;
  nickname?: string;
  password?: string;
  phone?: string;
  email?: string;
  gender?: number;
  status?: number;
  avatar?: string;
  roleIds?: ID[];
  remark?: string;
}

// 用户详细信息
export interface UserDetailRecord {
  id: ID;
  username: string;
  nickname: string;
  avatar: string;
  phone: string;
  email: string;
  gender: number;
  status: number;
  roles: RoleRecord[];
  remark?: string;
  lastLoginTime?: string;
  lastLoginIp?: string;
  createTime: string;
  updateTime: string;
  createBy?: string;
  updateBy?: string;
}

// 用户状态常量
export const UserStatus = {
  DISABLED: 0,
  ENABLED: 1,
} as const;

// 用户性别常量
export const UserGender = {
  UNKNOWN: 0,
  MALE: 1,
  FEMALE: 2,
} as const;

// 重置密码参数
export interface ResetPasswordParams {
  password: string;
  confirmPassword?: string;
}

// 用户状态切换参数
export interface UserStatusParams {
  status: number;
  reason?: string;
}

// 用户统计信息
export interface UserStatsInfo {
  total: number;
  active: number;
  inactive: number;
  newThisMonth: number;
  onlineCount: number;
}

// 用户导入数据
export interface UserImportData {
  username: string;
  nickname?: string;
  phone?: string;
  email?: string;
  gender?: number;
  roleKeys?: string[];
}

// 用户导入结果
export interface UserImportResult {
  success: number;
  failed: number;
  total: number;
  errors?: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

// 用户在线信息
export interface UserOnlineInfo {
  id: ID;
  username: string;
  nickname: string;
  loginTime: string;
  loginIp: string;
  browser?: string;
  os?: string;
  location?: string;
}

// 用户操作日志
export interface UserOperationLog {
  id: ID;
  userId: ID;
  username: string;
  operation: string;
  method: string;
  params?: string;
  result?: string;
  ip: string;
  location?: string;
  browser?: string;
  createTime: string;
}

// 用户操作类型
export const UserOperationType = {
  LOGIN: 'login',
  LOGOUT: 'logout',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  RESET_PASSWORD: 'reset_password',
  CHANGE_STATUS: 'change_status',
  ASSIGN_ROLE: 'assign_role',
} as const;

export type UserOperationTypeValue = typeof UserOperationType[keyof typeof UserOperationType];
