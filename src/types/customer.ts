/**
 * 客户管理相关类型定义
 */

import type { PageParams, ID } from './api';

// 客户信息
export interface CustomerRecord {
  id: ID;
  name: string;
  phone: string;
  email?: string;
  gender?: number;
  age?: number;
  birthday?: string;
  address?: string;
  source?: string; // 客户来源
  status: number; // 客户状态
  tags?: string[]; // 客户标签
  remark?: string; // 备注
  createTime: string;
  updateTime: string;
  createBy?: string;
  updateBy?: string;
}

// 客户查询参数
export interface CustomerQueryParams extends PageParams {
  name?: string;
  phone?: string;
  email?: string;
  status?: number;
  source?: string;
  gender?: number;
  ageMin?: number;
  ageMax?: number;
  tags?: string[];
  startDate?: string;
  endDate?: string;
  createBy?: string;
}

// 客户表单数据
export interface CustomerFormData {
  name: string;
  phone: string;
  email?: string;
  gender?: number;
  age?: number;
  birthday?: string;
  address?: string;
  source?: string;
  status?: number;
  tags?: string[];
  remark?: string;
}

// 客户状态常量
export const CustomerStatus = {
  INACTIVE: 0,    // 非活跃
  ACTIVE: 1,      // 活跃
  POTENTIAL: 2,   // 潜在客户
  LOST: 3,        // 流失客户
} as const;

// 客户来源常量
export const CustomerSource = {
  ONLINE: 'online',           // 线上
  OFFLINE: 'offline',         // 线下
  REFERRAL: 'referral',       // 推荐
  ADVERTISEMENT: 'ad',        // 广告
  SOCIAL_MEDIA: 'social',     // 社交媒体
  PHONE: 'phone',             // 电话
  EMAIL: 'email',             // 邮件
  OTHER: 'other',             // 其他
} as const;

// 客户性别常量
export const CustomerGender = {
  UNKNOWN: 0,
  MALE: 1,
  FEMALE: 2,
} as const;

// 客户统计信息
export interface CustomerStatsInfo {
  total: number;
  active: number;
  inactive: number;
  potential: number;
  lost: number;
  newThisMonth: number;
  newThisWeek: number;
  newToday: number;
}

// 客户导入数据
export interface CustomerImportData {
  name: string;
  phone: string;
  email?: string;
  gender?: number;
  age?: number;
  address?: string;
  source?: string;
  tags?: string;
  remark?: string;
}

// 客户导入结果
export interface CustomerImportResult {
  success: number;
  failed: number;
  total: number;
  errors?: Array<{
    row: number;
    field: string;
    message: string;
    data?: CustomerImportData;
  }>;
}

// 客户导出参数
export interface CustomerExportParams extends CustomerQueryParams {
  fields?: string[]; // 导出字段
  format?: 'xlsx' | 'csv'; // 导出格式
}

// 客户标签
export interface CustomerTag {
  id: ID;
  name: string;
  color?: string;
  description?: string;
  createTime: string;
}

// 客户跟进记录
export interface CustomerFollowRecord {
  id: ID;
  customerId: ID;
  content: string;
  type: string; // 跟进类型：电话、邮件、面谈等
  nextFollowTime?: string;
  status: number;
  createTime: string;
  createBy: string;
  createByName: string;
}

// 客户跟进类型
export const CustomerFollowType = {
  PHONE: 'phone',         // 电话
  EMAIL: 'email',         // 邮件
  MEETING: 'meeting',     // 面谈
  VISIT: 'visit',         // 拜访
  WECHAT: 'wechat',       // 微信
  QQ: 'qq',               // QQ
  OTHER: 'other',         // 其他
} as const;

// 客户跟进状态
export const CustomerFollowStatus = {
  PENDING: 0,     // 待跟进
  COMPLETED: 1,   // 已完成
  CANCELLED: 2,   // 已取消
} as const;

// 客户分析数据
export interface CustomerAnalysisData {
  sourceDistribution: Array<{
    source: string;
    count: number;
    percentage: number;
  }>;
  statusDistribution: Array<{
    status: number;
    count: number;
    percentage: number;
  }>;
  ageDistribution: Array<{
    ageRange: string;
    count: number;
    percentage: number;
  }>;
  genderDistribution: Array<{
    gender: number;
    count: number;
    percentage: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    newCustomers: number;
    activeCustomers: number;
  }>;
}

export type CustomerStatusValue = typeof CustomerStatus[keyof typeof CustomerStatus];
export type CustomerSourceValue = typeof CustomerSource[keyof typeof CustomerSource];
export type CustomerFollowTypeValue = typeof CustomerFollowType[keyof typeof CustomerFollowType];
