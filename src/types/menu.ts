/**
 * 菜单管理相关类型定义
 */

import type { PageParams, ID, TreeNode } from './api';
import type { MenuRecord } from './auth';

// 菜单查询参数
export interface MenuQueryParams extends PageParams {
  title?: string;
  name?: string;
  type?: number;
  status?: number;
  parentId?: ID;
  visible?: boolean;
}

// 菜单表单数据
export interface MenuFormData {
  parentId?: ID;
  name: string;
  title: string;
  type: number; // 1-菜单 2-按钮 3-目录
  path?: string;
  component?: string;
  icon?: string;
  sort?: number;
  status?: number;
  visible?: boolean;
  permission?: string;
  redirect?: string;
  keepAlive?: boolean;
  alwaysShow?: boolean;
  breadcrumb?: boolean;
  affix?: boolean;
  noTagsView?: boolean;
  remark?: string;
}

// 扩展菜单信息
export interface MenuDetailRecord extends MenuRecord {
  sort: number;
  status: number;
  visible: boolean;
  permission?: string;
  redirect?: string;
  keepAlive?: boolean;
  alwaysShow?: boolean;
  breadcrumb?: boolean;
  affix?: boolean;
  noTagsView?: boolean;
  remark?: string;
  createTime: string;
  updateTime: string;
  createBy?: string;
  updateBy?: string;
}

// 菜单状态常量
export const MenuStatus = {
  DISABLED: 0,
  ENABLED: 1,
} as const;

// 菜单类型常量（重新导出以保持一致性）
export const MenuTypeEnum = {
  MENU: 1,
  BUTTON: 2,
  DIRECTORY: 3,
} as const;

// 菜单移动位置
export const MenuMovePosition = {
  BEFORE: 'before',
  AFTER: 'after',
  INSIDE: 'inside',
} as const;

// 菜单移动参数
export interface MenuMoveParams {
  targetId: ID;
  position: typeof MenuMovePosition[keyof typeof MenuMovePosition];
}

// 菜单排序参数
export interface MenuSortParams {
  id: ID;
  sort: number;
}

// 菜单状态切换参数
export interface MenuStatusParams {
  status: number;
  reason?: string;
}

// 菜单树节点（用于选择父菜单）
export interface MenuTreeNode extends TreeNode {
  id: ID;
  parentId?: ID;
  title: string;
  name: string;
  type: number;
  icon?: string;
  disabled?: boolean;
  children?: MenuTreeNode[];
}

// 菜单路由元信息
export interface MenuRouteMeta {
  title: string;
  icon?: string;
  keepAlive?: boolean;
  alwaysShow?: boolean;
  breadcrumb?: boolean;
  affix?: boolean;
  noTagsView?: boolean;
  permission?: string;
  roles?: string[];
}

// 菜单路由配置
export interface MenuRouteConfig {
  id: ID;
  name: string;
  path: string;
  component?: string;
  redirect?: string;
  meta: MenuRouteMeta;
  children?: MenuRouteConfig[];
}

// 菜单权限信息
export interface MenuPermissionInfo {
  id: ID;
  title: string;
  permission: string;
  type: number;
  parentId?: ID;
  children?: MenuPermissionInfo[];
}

// 菜单统计信息
export interface MenuStatsInfo {
  total: number;
  menus: number;
  buttons: number;
  directories: number;
  enabled: number;
  disabled: number;
  visible: number;
  hidden: number;
}

// 菜单图标配置
export interface MenuIconConfig {
  type: 'antd' | 'custom' | 'svg';
  value: string;
  color?: string;
  size?: number;
}

// 菜单缓存配置
export interface MenuCacheConfig {
  keepAlive: boolean;
  include?: string[];
  exclude?: string[];
  max?: number;
}

// 菜单面包屑项
export interface MenuBreadcrumbItem {
  title: string;
  path?: string;
  icon?: string;
}

// 菜单导航项
export interface MenuNavItem {
  id: ID;
  title: string;
  path: string;
  icon?: string;
  badge?: number | string;
  children?: MenuNavItem[];
}

// 菜单操作日志
export interface MenuOperationLog {
  id: ID;
  menuId: ID;
  menuTitle: string;
  operation: string;
  operationData?: any;
  operatorId: ID;
  operatorName: string;
  ip: string;
  createTime: string;
}

// 菜单操作类型
export const MenuOperationType = {
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  MOVE: 'move',
  SORT: 'sort',
  CHANGE_STATUS: 'change_status',
  CHANGE_VISIBLE: 'change_visible',
} as const;

// ===== Ant Design Vue 菜单类型定义 =====

/**
 * Ant Design Vue 菜单项基础接口
 */
export interface AntdMenuItemBase {
  /** 菜单项的唯一标识 */
  key: string;
  /** 菜单项标题 */
  title?: string;
  /** 菜单项标签（显示文本） */
  label?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 菜单项类型 */
  type?: 'group' | 'divider';
  /** 菜单项图标 */
  icon?: any;
  /** 菜单项的额外属性 */
  extra?: any;
  /** 菜单项的危险状态 */
  danger?: boolean;
}

/**
 * Ant Design Vue 普通菜单项
 */
export interface AntdMenuItem extends AntdMenuItemBase {
  /** 菜单项类型 - 普通菜单项不设置此属性 */
  type?: never;
  /** 子菜单项 - 普通菜单项不包含子项 */
  children?: never;
}

/**
 * Ant Design Vue 子菜单项
 */
export interface AntdSubMenuItem extends AntdMenuItemBase {
  /** 子菜单项列表 */
  children: AntdMenuItemType[];
  /** 子菜单的弹出位置 */
  popupClassName?: string;
  /** 子菜单的弹出偏移 */
  popupOffset?: [number, number];
}

/**
 * Ant Design Vue 菜单分组
 */
export interface AntdMenuGroup extends AntdMenuItemBase {
  /** 菜单项类型 */
  type: 'group';
  /** 分组内的菜单项 */
  children: AntdMenuItemType[];
}

/**
 * Ant Design Vue 菜单分割线
 */
export interface AntdMenuDivider extends AntdMenuItemBase {
  /** 菜单项类型 */
  type: 'divider';
  /** 分割线不包含子项 */
  children?: never;
}

/**
 * Ant Design Vue 菜单项联合类型
 */
export type AntdMenuItemType =
  | AntdMenuItem
  | AntdSubMenuItem
  | AntdMenuGroup
  | AntdMenuDivider;

/**
 * 菜单转换配置
 */
export interface MenuTransformConfig {
  /** 是否显示图标 */
  showIcon?: boolean;
  /** 是否过滤隐藏菜单 */
  filterHidden?: boolean;
  /** 是否过滤按钮类型菜单 */
  filterButtons?: boolean;
  /** 图标映射函数 */
  iconMapper?: (iconName: string) => any;
  /** 自定义标签生成函数 */
  labelGenerator?: (menu: MenuRecord) => string;
}

export type MenuStatusValue = typeof MenuStatus[keyof typeof MenuStatus];
export type MenuTypeValue = typeof MenuTypeEnum[keyof typeof MenuTypeEnum];
export type MenuMovePositionValue = typeof MenuMovePosition[keyof typeof MenuMovePosition];
export type MenuOperationTypeValue = typeof MenuOperationType[keyof typeof MenuOperationType];
