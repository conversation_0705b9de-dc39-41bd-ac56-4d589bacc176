/* 全局主题CSS变量样式 */

/* 背景色工具类 */
.bg-base {
  background-color: var(--theme-bg-base);
}

/* 容器色 */
.bg-container {
  background-color: var(--theme-bg-container);
}

/* 布局色 */
.bg-layout {
  background-color: var(--theme-bg-layout);
}

/* 悬浮色 */
.bg-elevated {
  background-color: var(--theme-bg-elevated);
}

/* 主题色 */
.bg-primary {
  background-color: var(--theme-primary);
}

/* 主题色浅色 */
.bg-primary-light {
  background-color: var(--theme-primary-bg);
}

/* 文字色工具类 */
.text-base {
  color: var(--theme-text-base);
}

/* 文本主题色 */
.text-primary {
  color: var(--theme-text);
}

/* 文本主题色浅色 */
.text-secondary {
  color: var(--theme-text-secondary);
}

/* 文本主题色浅色 */
.text-tertiary {
  color: var(--theme-text-tertiary);
}

/* 文本主题色浅色 */
.text-quaternary {
  color: var(--theme-text-quaternary);
}

/* 文本主题色 */
.text-theme-primary {
  color: var(--theme-primary);
}

/* 边框工具类 */
.border-primary {
  border-color: var(--theme-border);
}

/* 边框主题色浅色 */
.border-secondary {
  border-color: var(--theme-border-secondary);
}

/* 边框主题色 */
.border-theme-primary {
  border-color: var(--theme-primary);
}

/* 文本 - 状态色工具类 */
.text-success {
  color: var(--theme-success);
}

/* 文本 - 警告色 */
.text-warning {
  color: var(--theme-warning);
}

/* 文本 - 错误色 */
.text-error {
  color: var(--theme-error);
}

/* 文本 - 信息色 */
.text-info {
  color: var(--theme-info);
}

/* 背景 - 成功色 */
.bg-success {
  background-color: var(--theme-success);
}

/* 背景 - 警告色 */
.bg-warning {
  background-color: var(--theme-warning);
}

/* 背景 - 错误色 */
.bg-error {
  background-color: var(--theme-error);
}

/* 背景 - 信息色 */
.bg-info {
  background-color: var(--theme-info);
}

/* 背景 - 成功色浅色 */
.bg-success-light {
  background-color: var(--theme-success-bg);
}

/* 背景 - 警告色浅色 */
.bg-warning-light {
  background-color: var(--theme-warning-bg);
}

/* 背景 - 错误色浅色 */
.bg-error-light {
  background-color: var(--theme-error-bg);
}

/* 背景 - 信息色浅色 */
.bg-info-light {
  background-color: var(--theme-info-bg);
}

/* 填充色工具类 */
.fill-primary {
  background-color: var(--theme-fill);
}

/* 填充色浅色 */
.fill-secondary {
  background-color: var(--theme-fill-secondary);
}

/* 填充色浅色 */
.fill-tertiary {
  background-color: var(--theme-fill-tertiary);
}

/* 填充色浅色 */
.fill-quaternary {
  background-color: var(--theme-fill-quaternary);
}

/* 主题卡片样式 */
.theme-card {
  background-color: var(--theme-bg-container);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  padding: 16px;
}

/* 主题卡片悬浮样式 */
.theme-card-elevated {
  background-color: var(--theme-bg-elevated);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 主题按钮样式 */
.theme-button {
  background-color: var(--theme-primary);
  color: var(--theme-bg-base);
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 主题按钮悬浮样式 */
.theme-button:hover {
  background-color: var(--theme-primary-hover);
  border-color: var(--theme-primary-hover);
}

/* 主题按钮激活样式 */
.theme-button:active {
  background-color: var(--theme-primary-active);
  border-color: var(--theme-primary-active);
}

/* 主题按钮轮廓样式 */
.theme-button-outline {
  background-color: transparent;
  color: var(--theme-primary);
  border: 1px solid var(--theme-primary);
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 主题按钮轮廓悬浮样式 */
.theme-button-outline:hover {
  background-color: var(--theme-primary-bg);
  color: var(--theme-primary-hover);
  border-color: var(--theme-primary-hover);
}

/* 主题输入框样式 */
.theme-input {
  background-color: var(--theme-bg-container);
  color: var(--theme-text);
  border: 1px solid var(--theme-border);
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.3s ease;
}

/* 主题输入框聚焦样式 */
.theme-input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px var(--theme-primary-bg);
  outline: none;
}

/* 响应式主题页面背景 */
.theme-page {
  background-color: var(--theme-bg-layout);
  color: var(--theme-text);
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 主题分割线 */
.theme-divider {
  border-color: var(--theme-border);
}

/* 主题阴影 */
.theme-shadow {
  box-shadow: 0 2px 8px var(--theme-fill-quaternary);
}

.theme-shadow-lg {
  box-shadow: 0 4px 16px var(--theme-fill-tertiary);
}

/* 动画过渡 */
.theme-transition {
  transition: background-color 0.3s ease, 
              color 0.3s ease, 
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}

/* 主题特定的滚动条样式 */
.theme-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 主题滚动条轨道样式 */
.theme-scrollbar::-webkit-scrollbar-track {
  background: var(--theme-fill-quaternary);
  border-radius: 3px;
}

/* 主题滚动条滑块样式 */
.theme-scrollbar::-webkit-scrollbar-thumb {
  background: var(--theme-fill-secondary);
  border-radius: 3px;
}

/* 主题滚动条滑块悬浮样式 */
.theme-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--theme-fill);
} 