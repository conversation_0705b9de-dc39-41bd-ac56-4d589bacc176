import { theme } from "ant-design-vue";
import { reactive } from "vue";

// 预设主题类型Key
export const enum PreTheme {
  default = "default",
  dark = "dark",
  compact = "compact",
  custom = "custom",
}

// 主题信息配置
export const themeInfo = {
  default: {
    name: "默认主题",
    description: "标准的 Ant Design 主题",
    preview: "linear-gradient(45deg, #1890ff, #52c41a)",
  },
  dark: {
    name: "暗色主题",
    description: "适合夜间使用的深色主题",
    preview: "linear-gradient(45deg, #434343, #262626)",
  },
  compact: {
    name: "紧凑主题",
    description: "更紧凑的布局和间距",
    preview: "linear-gradient(45deg, #1890ff, #722ed1)",
  },
  custom: {
    name: "自定义主题",
    description: "自定义品牌主题",
    preview: "linear-gradient(45deg, #6366f1, #8b5cf6)",
  },
} as const;

// 存储localStorage的key
const storageKey = "app-theme";

// 默认的主题色：红色, 代表没有设置过
const defaultColor = "#1f74ec";

// 默认的主题算法映射
// 直接创建 Map
const algorithmsMap = new Map([
  [PreTheme.default, theme.defaultAlgorithm],
  [PreTheme.dark, theme.darkAlgorithm],
  [PreTheme.compact, theme.compactAlgorithm],
  [PreTheme.custom, theme.defaultAlgorithm], // 自定义主题
]);
// 应用主题配置参数
interface ApplyThemeConfig {
  /** 预设主题类型Key:用于获取主题*/
  themeKey: PreTheme;
  /** 主题名称 */
  themeName:string;
  /** 主题颜色 */
  token?: {
    colorPrimary: string
  };
  /** antd主题，通过preTheme获取 */
  algorithm: () => Record<string, any>,
  /** 提示消息 */
  message?: string;
}

/** 响应式主题状态 */
export const themeState = reactive<{
  theme: ApplyThemeConfig;
  subscribers: Set<(theme: ApplyThemeConfig) => void>;
}>({
  theme: {
    themeKey: PreTheme.default,
    themeName:themeInfo[PreTheme.default].name,
    token:{
      colorPrimary: defaultColor
    },
    algorithm: () => algorithmsMap.get(PreTheme.default) || theme.defaultAlgorithm,
  },
  subscribers: new Set(),
});

/** 应用主题并通知订阅者 */
export const applyTheme = (config: Partial<ApplyThemeConfig>) => {
  // 1. 合并配置
  const newTheme = { ...themeState.theme, ...config };

  // 2. 验证并获取算法
  if (algorithmsMap.has(newTheme.themeKey)) {
    newTheme.algorithm = () => algorithmsMap.get(newTheme.themeKey)!;
    delete newTheme.message;
  } else {
    newTheme.themeKey = PreTheme.default;
    newTheme.algorithm = () => algorithmsMap.get(PreTheme.default)!;
    newTheme.message = '⚠️ 未识别主题Key，已回退默认主题';
  }
  newTheme.themeName = themeInfo[newTheme.themeKey].name
  // 3. 更新状态
  Object.assign(themeState.theme, newTheme);
  localStorage.setItem(storageKey, JSON.stringify(newTheme));

  // 4. 通知所有订阅者
  themeState.subscribers.forEach(sub => sub(themeState.theme));
};

/** 重置主题 */
export const reset = () => {
  applyTheme(build(themeState.theme.themeKey, defaultColor))
}

/** 订阅主题变化 */
export const subscribeTheme = (callback: (theme: ApplyThemeConfig) => void) => {
  themeState.subscribers.add(callback);
  return () => themeState.subscribers.delete(callback);
};

// 初始化时从本地存储加载
const savedConfig = localStorage.getItem(storageKey);
if (savedConfig) {
  applyTheme(JSON.parse(savedConfig));
} else {
  applyTheme(themeState.theme);
  localStorage.setItem(storageKey, JSON.stringify(themeState.theme));
}

/** 构建主题对象函数 */
export const build = (preTheme:PreTheme, color?:string):ApplyThemeConfig => {
  return {
    themeKey: preTheme,
    themeName: themeInfo[preTheme].name,
    token:{
      colorPrimary: color || defaultColor
    },
    algorithm: () => algorithmsMap.get(preTheme) || theme.defaultAlgorithm
  }
}

// 工具函数：获取所有可用主题
export function getAllThemes(): Array<{
  key: PreTheme;
  info: (typeof themeInfo)[PreTheme];
}> {
  return Object.entries(themeInfo).map(([key, info]) => ({
    key: key as PreTheme,
    info,
  }));
}