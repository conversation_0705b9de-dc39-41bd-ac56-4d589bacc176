<!--
  菜单测试页面
  用于测试动态菜单渲染功能
-->
<template>
  <div class="menu-test-page">
    <!-- 菜单演示组件 -->
    <MenuDemo />

    <!-- 详细信息展示 -->
    <a-card title="详细信息" class="detail-card" style="margin-top: 24px;">
      <a-tabs>
        <a-tab-pane key="raw" tab="原始菜单数据">
          <pre class="json-display">{{ JSON.stringify(rawMenus, null, 2) }}</pre>
        </a-tab-pane>
        <a-tab-pane key="transformed" tab="转换后菜单数据">
          <pre class="json-display">{{ JSON.stringify(menuItems, null, 2) }}</pre>
        </a-tab-pane>
        <a-tab-pane key="state" tab="菜单状态">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="当前选中菜单">
              {{ selectedKeys.join(', ') || '无' }}
            </a-descriptions-item>
            <a-descriptions-item label="当前展开菜单">
              {{ openKeys.join(', ') || '无' }}
            </a-descriptions-item>
            <a-descriptions-item label="当前路由菜单键">
              {{ currentMenuKey || '无' }}
            </a-descriptions-item>
            <a-descriptions-item label="父级菜单键">
              {{ currentParentKeys.join(', ') || '无' }}
            </a-descriptions-item>
            <a-descriptions-item label="所有菜单键">
              {{ allMenuKeys.join(', ') }}
            </a-descriptions-item>
            <a-descriptions-item label="菜单加载状态">
              {{ menuLoading ? '加载中' : '已加载' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import MenuDemo from '@/components/MenuDemo.vue';
import { useMenu } from '@/composables/useMenu';

// 菜单管理（用于详细信息展示）
const {
  selectedKeys,
  openKeys,
  menuItems,
  rawMenus,
  allMenuKeys,
  currentMenuKey,
  currentParentKeys,
  menuLoading,
} = useMenu();
</script>

<style scoped>
.menu-test-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.detail-card {
  max-width: 1200px;
  margin: 0 auto;
}

.json-display {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #262626;
}

:deep(.ant-descriptions-item-content) {
  color: #595959;
}
</style>
