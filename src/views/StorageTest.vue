<template>
  <div class="storage-test">
    <a-card title="存储功能测试" style="margin-bottom: 16px;">
      <a-space direction="vertical" size="large" style="width: 100%;">
        
        <!-- 认证存储测试 -->
        <a-card size="small" title="认证存储测试">
          <a-space direction="vertical" style="width: 100%;">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-input 
                  v-model:value="testToken" 
                  placeholder="输入测试Token"
                  addon-before="Token"
                />
              </a-col>
              <a-col :span="12">
                <a-space>
                  <a-button @click="saveToken" type="primary">保存Token</a-button>
                  <a-button @click="loadToken">读取Token</a-button>
                  <a-button @click="clearToken" danger>清除Token</a-button>
                </a-space>
              </a-col>
            </a-row>
            
            <a-alert 
              v-if="tokenResult" 
              :message="tokenResult" 
              type="info" 
              show-icon 
            />
          </a-space>
        </a-card>

        <!-- 用户信息存储测试 -->
        <a-card size="small" title="用户信息存储测试">
          <a-space direction="vertical" style="width: 100%;">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-input 
                  v-model:value="testUser.username" 
                  placeholder="用户名"
                  addon-before="用户名"
                />
              </a-col>
              <a-col :span="8">
                <a-input 
                  v-model:value="testUser.nickname" 
                  placeholder="昵称"
                  addon-before="昵称"
                />
              </a-col>
              <a-col :span="8">
                <a-space>
                  <a-button @click="saveUserInfo" type="primary">保存用户信息</a-button>
                  <a-button @click="loadUserInfo">读取用户信息</a-button>
                </a-space>
              </a-col>
            </a-row>
            
            <a-alert 
              v-if="userResult" 
              :message="userResult" 
              type="info" 
              show-icon 
            />
          </a-space>
        </a-card>

        <!-- 用户偏好设置测试 -->
        <a-card size="small" title="用户偏好设置测试">
          <a-space direction="vertical" style="width: 100%;">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-select 
                  v-model:value="testPreferences.theme" 
                  placeholder="主题"
                  style="width: 100%;"
                >
                  <a-select-option value="light">浅色主题</a-select-option>
                  <a-select-option value="dark">深色主题</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-select 
                  v-model:value="testPreferences.language" 
                  placeholder="语言"
                  style="width: 100%;"
                >
                  <a-select-option value="zh-CN">中文</a-select-option>
                  <a-select-option value="en-US">English</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-input 
                  v-model:value="testPreferences.timezone" 
                  placeholder="时区"
                  addon-before="时区"
                />
              </a-col>
              <a-col :span="6">
                <a-space>
                  <a-button @click="savePreferences" type="primary">保存偏好</a-button>
                  <a-button @click="loadPreferences">读取偏好</a-button>
                </a-space>
              </a-col>
            </a-row>
            
            <a-alert 
              v-if="preferencesResult" 
              :message="preferencesResult" 
              type="info" 
              show-icon 
            />
          </a-space>
        </a-card>

        <!-- 存储信息 -->
        <a-card size="small" title="存储信息">
          <a-space direction="vertical" style="width: 100%;">
            <a-button @click="getStorageInfo" type="default">获取存储信息</a-button>
            <a-alert 
              v-if="storageInfo" 
              :message="storageInfo" 
              type="info" 
              show-icon 
            />
          </a-space>
        </a-card>

        <!-- 清除所有数据 -->
        <a-card size="small" title="危险操作">
          <a-space>
            <a-button @click="clearAllAuth" danger>清除所有认证数据</a-button>
            <a-button @click="clearAllStorage" danger>清除所有存储数据</a-button>
          </a-space>
        </a-card>

      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import { AuthStorageManager, StorageManager } from '@/utils/storage';

// 测试数据
const testToken = ref('test-token-12345');
const testUser = ref({
  username: 'testuser',
  nickname: '测试用户'
});
const testPreferences = ref({
  theme: 'light',
  language: 'zh-CN',
  timezone: 'Asia/Shanghai'
});

// 结果显示
const tokenResult = ref('');
const userResult = ref('');
const preferencesResult = ref('');
const storageInfo = ref('');

// Token测试
const saveToken = () => {
  AuthStorageManager.setAccessToken(testToken.value);
  tokenResult.value = `Token已保存: ${testToken.value}`;
  message.success('Token保存成功');
};

const loadToken = () => {
  const token = AuthStorageManager.getAccessToken();
  tokenResult.value = token ? `读取到Token: ${token}` : '未找到Token';
};

const clearToken = () => {
  AuthStorageManager.clearAuthData();
  tokenResult.value = '所有认证数据已清除';
  message.success('Token清除成功');
};

// 用户信息测试
const saveUserInfo = () => {
  AuthStorageManager.setUserInfo(testUser.value);
  userResult.value = `用户信息已保存: ${JSON.stringify(testUser.value)}`;
  message.success('用户信息保存成功');
};

const loadUserInfo = () => {
  const userInfo = AuthStorageManager.getUserInfo();
  userResult.value = userInfo ? `读取到用户信息: ${JSON.stringify(userInfo)}` : '未找到用户信息';
};

// 偏好设置测试
const savePreferences = () => {
  StorageManager.setItem('test_preferences', testPreferences.value, -1);
  preferencesResult.value = `偏好设置已保存: ${JSON.stringify(testPreferences.value)}`;
  message.success('偏好设置保存成功');
};

const loadPreferences = () => {
  const preferences = StorageManager.getItem('test_preferences');
  preferencesResult.value = preferences ? `读取到偏好设置: ${JSON.stringify(preferences)}` : '未找到偏好设置';
};

// 存储信息
const getStorageInfo = () => {
  const info = StorageManager.getStorageInfo();
  storageInfo.value = `已使用: ${(info.used / 1024).toFixed(2)}KB, 总容量: ${(info.total / 1024 / 1024).toFixed(2)}MB, 可用: ${(info.available / 1024).toFixed(2)}KB`;
};

// 清除数据
const clearAllAuth = () => {
  AuthStorageManager.clearAuthData();
  message.success('所有认证数据已清除');
};

const clearAllStorage = () => {
  StorageManager.clear();
  message.success('所有存储数据已清除');
};
</script>

<style scoped>
.storage-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.storage-test :deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

.storage-test :deep(.ant-alert-message) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style>
