<template>
  <div class="persist-test-page">
    <div class="test-container">
      <h2>Pinia Plugin Persist 测试</h2>
      
      <!-- 当前状态显示 -->
      <div class="status-section">
        <h3>当前认证状态</h3>
        <div class="status-item">
          <strong>登录状态:</strong> {{ authStore.loginStatus }}
        </div>
        <div class="status-item">
          <strong>Access Token:</strong> {{ authStore.accessToken ? '已设置' : '未设置' }}
        </div>
        <div class="status-item">
          <strong>用户信息:</strong> {{ authStore.userInfo ? '已设置' : '未设置' }}
        </div>
        <div class="status-item">
          <strong>菜单数量:</strong> {{ authStore.userMenus.length }}
        </div>
      </div>

      <!-- localStorage 数据显示 -->
      <div class="storage-section">
        <h3>LocalStorage 数据</h3>
        <div class="storage-item">
          <strong>auth-store 键:</strong>
          <pre>{{ localStorageData }}</pre>
        </div>
      </div>

      <!-- 测试按钮 -->
      <div class="actions-section">
        <h3>测试操作</h3>
        <a-space>
          <a-button type="primary" @click="testSetData">设置测试数据</a-button>
          <a-button @click="testClearData">清除数据</a-button>
          <a-button @click="refreshData">刷新显示</a-button>
          <a-button @click="simulateLogin">模拟登录</a-button>
        </a-space>
      </div>

      <!-- 调试日志 -->
      <div class="logs-section">
        <h3>调试日志</h3>
        <div class="logs-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useAuthStore } from '@/stores/modules/auth';
import { LoginStatus } from '@/types/auth';

const authStore = useAuthStore();
const localStorageData = ref<string>('');
const logs = ref<Array<{ time: string, message: string }>>([]);

const addLog = (message: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  });
  console.log(`🔍 [PersistTest] ${message}`);
};

const refreshData = () => {
  const data = localStorage.getItem('auth-store');
  localStorageData.value = data ? JSON.stringify(JSON.parse(data), null, 2) : '无数据';
  addLog('刷新了显示数据');
};

const testSetData = () => {
  // 设置一些测试数据
  authStore.loginStatus = LoginStatus.LOGGED_IN;
  authStore.accessToken = 'test-token-' + Date.now();
  authStore.userInfo = {
    id: 'test-user',
    username: 'testuser',
    nickname: '测试用户',
    avatar: '',
    email: '<EMAIL>',
    phone: '13800138000',
    status: 1,
    roles: []
  };
  
  addLog('设置了测试数据');
  setTimeout(refreshData, 100); // 稍等一下让插件保存数据
};

const testClearData = () => {
  authStore.clearAuthData();
  addLog('清除了认证数据');
  setTimeout(refreshData, 100);
};

const simulateLogin = async () => {
  try {
    addLog('开始模拟登录...');
    
    // 模拟登录数据
    const mockAuthRecord = {
      userInfo: {
        id: 'mock-user-123',
        username: 'mockuser',
        nickname: '模拟用户',
        avatar: '',
        email: '<EMAIL>',
        phone: '13900139000',
        status: 1,
        roles: [{ key: 'user', name: '普通用户' }]
      },
      authInfo: {
        accessToken: 'mock-token-' + Date.now(),
        menus: [
          {
            id: 'dashboard',
            name: '仪表盘',
            path: '/dashboard',
            icon: 'DashboardOutlined',
            children: []
          }
        ]
      }
    };
    
    await authStore.setAuthData(mockAuthRecord);
    authStore.loginStatus = LoginStatus.LOGGED_IN;
    
    addLog('模拟登录成功');
    setTimeout(refreshData, 100);
  } catch (error) {
    addLog(`模拟登录失败: ${error}`);
  }
};

onMounted(() => {
  addLog('组件已挂载');
  refreshData();
  
  // 监听 localStorage 变化
  window.addEventListener('storage', (e) => {
    if (e.key === 'auth-store') {
      addLog('检测到 localStorage 变化');
      refreshData();
    }
  });
});
</script>

<style scoped>
.persist-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-section,
.storage-section,
.actions-section,
.logs-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
}

.status-item,
.storage-item {
  margin-bottom: 8px;
}

.storage-item pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 4px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 8px;
}

.log-message {
  color: #333;
}
</style>
