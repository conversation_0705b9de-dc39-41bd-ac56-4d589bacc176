<template>
  <div class="dashboard">
    <a-row :gutter="[16, 16]">
      <!-- 统计卡片 -->
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="今日客户"
            :value="todayCustomers"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="今日预约"
            :value="todayAppointments"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <CalendarOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="今日收入"
            :value="todayIncome"
            :precision="2"
            :value-style="{ color: '#cf1322' }"
            suffix="元"
          >
            <template #prefix>
              <MoneyCollectOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card class="stat-card">
          <a-statistic
            title="本月新增"
            :value="monthlyNewCustomers"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <TeamOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="[16, 16]" style="margin-top: 16px;">
      <!-- 快速操作 -->
      <a-col :xs="24" :lg="12">
        <a-card title="快速操作" class="quick-actions">
          <a-space direction="vertical" size="middle" style="width: 100%;">
            <a-button type="primary" size="large" block @click="handleQuickAction('add-customer')">
              <template #icon>
                <UserAddOutlined />
              </template>
              新增客户
            </a-button>
            
            <a-button size="large" block @click="handleQuickAction('add-appointment')">
              <template #icon>
                <CalendarOutlined />
              </template>
              预约管理
            </a-button>
            
            <a-button size="large" block @click="handleQuickAction('finance')">
              <template #icon>
                <MoneyCollectOutlined />
              </template>
              财务管理
            </a-button>
          </a-space>
        </a-card>
      </a-col>

      <!-- 最近活动 -->
      <a-col :xs="24" :lg="12">
        <a-card title="最近活动" class="recent-activities">
          <a-list
            :data-source="recentActivities"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      {{ item.type }}
                    </a-avatar>
                  </template>
                  <template #title>
                    {{ item.title }}
                  </template>
                  <template #description>
                    {{ item.time }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <!-- 欢迎信息 -->
    <a-card style="margin-top: 16px;" class="welcome-card">
      <a-result
        status="success"
        :title="`欢迎回来，${authStore.displayName}！`"
        :sub-title="`您已成功登录医美CRM管理系统，当前角色：${userRoleText}`"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="handleQuickAction('customer-list')">
              客户管理
            </a-button>
            <a-button @click="handleQuickAction('settings')">
              系统设置
            </a-button>
            <a-button @click="handleLogout" danger>
              退出登录
            </a-button>
          </a-space>
        </template>
      </a-result>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  UserOutlined,
  CalendarOutlined,
  MoneyCollectOutlined,
  TeamOutlined,
  UserAddOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useAuthStore } from '@/stores'
import { ROUTE_PATHS } from '@/constants/routes'

const router = useRouter()
const authStore = useAuthStore()

// 计算用户角色显示文本
const userRoleText = computed(() => {
  const roles = authStore.userRoles
  if (roles.length === 0) return '普通用户'

  const roleMap: Record<string, string> = {
    'admin': '系统管理员',
    'manager': '部门经理',
    'operator': '操作员',
    'user': '普通用户'
  }

  return roles.map(role => roleMap[role] || role).join('、')
})

// 模拟数据
const todayCustomers = ref(12)
const todayAppointments = ref(8)
const todayIncome = ref(15680.50)
const monthlyNewCustomers = ref(156)

const recentActivities = ref([
  {
    type: '客',
    title: '新客户张小姐完成注册',
    time: '2分钟前',
    color: '#1890ff',
  },
  {
    type: '约',
    title: '李女士预约了面部护理项目',
    time: '10分钟前',
    color: '#52c41a',
  },
  {
    type: '付',
    title: '王女士完成了项目付款',
    time: '30分钟前',
    color: '#faad14',
  },
  {
    type: '完',
    title: '陈女士完成了美容项目',
    time: '1小时前',
    color: '#722ed1',
  },
])

// 处理快速操作
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'add-customer':
      message.info('新增客户功能开发中...')
      break
    case 'add-appointment':
      message.info('预约管理功能开发中...')
      break
    case 'finance':
      message.info('财务管理功能开发中...')
      break
    case 'customer-list':
      message.info('客户管理功能开发中...')
      break
    case 'settings':
      message.info('系统设置功能开发中...')
      break
    default:
      message.info('功能开发中...')
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push(ROUTE_PATHS.LOGIN)
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stat-card {
  text-align: center;
}

.stat-card :deep(.ant-statistic-title) {
  color: #666;
  font-size: 14px;
}

.stat-card :deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

.quick-actions,
.recent-activities,
.welcome-card {
  height: 100%;
}

.quick-actions :deep(.ant-card-body) {
  padding: 24px;
}

.recent-activities :deep(.ant-list-item) {
  padding: 12px 0;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card :deep(.ant-result-title) {
  color: white;
}

.welcome-card :deep(.ant-result-subtitle) {
  color: rgba(255, 255, 255, 0.85);
}
</style>
