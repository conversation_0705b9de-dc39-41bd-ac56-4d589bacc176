<template>
  <div class="login-page theme-page">
    <div class="login-wrapper">
      <!-- 居中的登录容器 -->
      <transition name="card-fade" appear>
        <div class="login-container">
          <!-- 左侧登录表单区域 -->
          <div class="login-form-section">
            <!-- 登录标题 -->
            <div class="login-header">
              <h2>欢迎回来</h2>
              <p>输入您的凭据以访问您的账户</p>
              <!-- 主题信息展示卡片 -->
              <!-- <div class="theme-card theme-transition" style="margin-top: 12px; padding: 12px;">
                <small class="text-secondary">
                  当前主题: {{ themeState.theme.themeName }}
                </small>
                <div class="theme-colors-demo" style="margin-top: 8px; display: flex; gap: 8px;">
                  <span class="color-demo bg-primary" title="主题色"></span>
                  <span class="color-demo bg-success" title="成功色"></span>
                  <span class="color-demo bg-warning" title="警告色"></span>
                  <span class="color-demo bg-error" title="错误色"></span>
                </div>
              </div> -->
            </div>

            <!-- 登录表单 -->
            <a-form
              ref="formRef"
              :model="loginForm"
              class="login-form"
              layout="vertical"
              @finish="handleLogin"
            >
              <!-- 用户名登录模式 -->
              <template v-if="loginModeRef === 'username'">
                <!-- 用户名输入框 -->
                <a-form-item
                  label="用户名"
                  name="username"
                  :rules="[
                    { required: true, message: '请输入您的用户名' },
                    { pattern: /^[a-zA-Z0-9]+$/, message: '请输入有效的用户名' },
                  ]"
                >
                  <a-input
                    v-model:value="loginForm.username"
                    placeholder="请输入您的用户名"
                    size="large"
                  />
                </a-form-item>

                <!-- 密码输入框 -->
                <a-form-item
                  label="密码"
                  name="password"
                  :rules="[{ required: true, message: '请输入您的密码' }]"
                >
                  <a-input-password
                    v-model:value="loginForm.password"
                    placeholder="请输入您的密码"
                    size="large"
                  />
                </a-form-item>
              </template>

              <!-- 验证码登录模式 -->
              <template v-else-if="loginModeRef === 'sms'">
                <!-- 手机号输入框 -->
                <a-form-item
                  label="手机号"
                  name="mobile"
                  :rules="[
                    { required: true, message: '请输入手机号' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
                  ]"
                >
                  <a-input
                    v-model:value="loginForm.mobile"
                    placeholder="请输入手机号"
                    size="large"
                  />
                </a-form-item>

                <!-- 验证码输入框 -->
                <a-form-item
                  label="验证码"
                  name="smsCode"
                  :rules="[{ required: true, message: '请输入验证码' }]"
                >
                  <a-input-group compact>
                    <a-input
                      v-model:value="loginForm.smsCode"
                      placeholder="请输入验证码"
                      size="large"
                      style="width: calc(100% - 120px)"
                    />
                    <a-button
                      size="large"
                      :disabled="countdown > 0"
                      @click="handleGetSmsCode"
                      style="width: 120px"
                    >
                      {{
                        countdown > 0 ? `重新获取(${countdown}s)` : "获取验证码"
                      }}
                    </a-button>
                  </a-input-group>
                </a-form-item>
              </template>

              <!-- 登录按钮 -->
              <a-form-item>
                <a-button
                  type="primary"
                  html-type="submit"
                  size="large"
                  block
                  class="login-btn"
                  :loading="loading"
                >
                  登录
                </a-button>
              </a-form-item>
            </a-form>

            <!-- 分割线 -->
            <a-divider>其他方式登录</a-divider>

            <!-- 登录方式切换按钮 -->
            <div class="login-mode-buttons">
              <a-button
                class="mode-btn"
                size="large"
                type="default"
                @click="handleSwitchToSms"
              >
                <MobileOutlined />
                {{ loginModeRef === "sms" ? "用户名登录" : "验证码登录" }}
              </a-button>

              <a-button
                class="mode-btn"
                size="large"
                @click="handleWechatLogin"
              >
                <WechatOutlined />
                微信登录
              </a-button>
            </div>

            <!-- 底部条款 -->
            <div class="footer-terms">
              By clicking continue, you agree to our
              <a href="#" @click.prevent>Terms of Service</a>
              and
              <a href="#" @click.prevent>Privacy Policy</a>.
            </div>
          </div>

          <!-- 右侧Logo区域 -->
          <div class="logo-section">
            <div class="logo-content">
              <!-- 公司Logo图标 -->
              <div class="logo-icon">
                <svg
                  width="80"
                  height="80"
                  viewBox="0 0 80 80"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect width="80" height="80" rx="16" fill="#667eea" />
                  <rect
                    x="4"
                    y="4"
                    width="72"
                    height="72"
                    rx="12"
                    fill="#ffffff"
                    fill-opacity="0.1"
                  />
                  <path
                    d="M25 40L35 50L55 30"
                    stroke="#ffffff"
                    stroke-width="3"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <circle
                    cx="40"
                    cy="40"
                    r="25"
                    stroke="#ffffff"
                    stroke-width="2"
                    fill="none"
                    opacity="0.3"
                  />
                </svg>
              </div>

              <!-- 公司名称和副标题 -->
              <h1 class="logo-title">机构运营平台</h1>
              <p class="logo-subtitle">专业的医美机构客户管理平台</p>

              <!-- 装饰性元素 -->
              <div class="logo-features">
                <div class="feature-item">
                  <div class="feature-icon">✨</div>
                  <span>智能管理</span>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">🔒</div>
                  <span>安全可靠</span>
                </div>
                <div class="feature-item">
                  <div class="feature-icon">📊</div>
                  <span>数据分析</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- 微信登录模态框 -->
    <a-modal
      v-model:open="wechatModalVisible"
      title="微信扫码登录"
      :footer="null"
      :width="400"
      centered
    >
      <div class="wechat-qr-container">
        <div class="qr-code-placeholder">
          <div class="qr-code-box">
            <div class="qr-code-icon">
              <WechatOutlined style="font-size: 64px; color: #52c41a" />
            </div>
            <p class="qr-title">请使用微信扫描二维码登录</p>
            <p class="qr-tip">打开微信扫一扫功能扫描上方二维码</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { MobileOutlined, WechatOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import { ROUTE_PATHS } from "@/constants/routes";
import { useAuthStore } from "@/stores";
import type { UserLoginReq } from "@/types";

const router = useRouter();
const route = useRoute();

// 使用认证状态管理
const authStore = useAuthStore();

// 登录模式：'username' | 'sms'
const loginModeRef = ref<"username" | "sms">("username");

// 响应式数据
const loginForm = ref({
  username: "",
  password: "",
  mobile: "",
  smsCode: "",
});

const loading = ref(false);
const formRef = ref();

// 微信登录模态框
const wechatModalVisible = ref(false);

// 验证码倒计时
const countdown = ref(0);
let countdownTimer: number | null = 0;

// 登录方法
const handleLogin = async () => {
  try {
    loading.value = true;

    if (loginModeRef.value === "username") {
      // 用户名密码登录
      if (!loginForm.value.username || !loginForm.value.password) {
        message.error("请输入用户名和密码");
        return;
      }

      const loginData: UserLoginReq = {
        username: loginForm.value.username,
        password: loginForm.value.password,
      };

      // 调用登录API
      const success = await authStore.login(loginData);

      if (success) {
        // 登录成功，跳转到目标页面
        const redirect = (route.query.redirect as string) || ROUTE_PATHS.DASHBOARD;
        await router.push(redirect);
      }
    } else if (loginModeRef.value === "sms") {
      // 验证码登录（暂时使用模拟逻辑，后续可扩展SMS登录API）
      if (!loginForm.value.mobile || !loginForm.value.smsCode) {
        message.error("请输入手机号和验证码");
        return;
      }

      // TODO: 这里应该调用SMS登录API
      // 暂时使用用户名密码登录的方式，将手机号作为用户名
      const loginData: UserLoginReq = {
        username: loginForm.value.mobile,
        password: loginForm.value.smsCode, // 临时将验证码作为密码
      };

      const success = await authStore.login(loginData);

      if (success) {
        const redirect = (route.query.redirect as string) || ROUTE_PATHS.DASHBOARD;
        await router.push(redirect);
      }
    }
  } catch (error) {
    console.error("登录过程出错:", error);
    // 错误信息已经在store中处理，这里不需要重复显示
  } finally {
    loading.value = false;
  }
};

// 切换到验证码登录
const handleSwitchToSms = () => {
  if (loginModeRef.value === "sms") {
    // 如果已经是验证码模式，切换回邮箱模式
    loginModeRef.value = "username";
    // 清空表单
    loginForm.value.mobile = "";
    loginForm.value.smsCode = "";
    // 清除倒计时
    if (countdownTimer) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
    countdown.value = 0;
  } else {
    // 切换到验证码模式
    loginModeRef.value = "sms";
    // 清空表单
    loginForm.value.username = "";
    loginForm.value.password = "";
  }

  // 清除表单验证
  formRef.value?.clearValidate();
};

// 微信登录
const handleWechatLogin = () => {
  wechatModalVisible.value = true;
};

// 获取短信验证码
const handleGetSmsCode = () => {
  if (!loginForm.value.mobile) {
    message.warning("请先输入手机号");
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.value.mobile)) {
    message.warning("请输入正确的手机号");
    return;
  }

  // 开始倒计时
  countdown.value = 60;
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer!);
      countdownTimer = null;
    }
  }, 1000);

  message.success("验证码已发送，请注意查收");
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer as number);
  }
});
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-bg-layout);
  padding: 20px;
}

.login-wrapper {
  width: 100%;
  max-width: 1000px;
  display: flex;
  justify-content: center;
}

.login-container {
  display: flex;
  background: var(--theme-bg-container);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 600px;
  width: 100%;
  max-width: 900px;
}

/* 左侧登录表单区域 */
.login-form-section {
  flex: 1;
  padding: 48px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 右侧Logo区域 */
.logo-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.logo-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("@/assets/images/login-right-3.avif");
  pointer-events: none;
}

.logo-content {
  text-align: center;
  color: var(--theme-text-secondary);
  z-index: 1;
  position: relative;
  padding: 40px;
  max-width: 350px;
}

.logo-icon {
  margin-bottom: 24px;
  animation: float 3s ease-in-out infinite;
}

.logo-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 12px;
  color: var(--theme-text-base);
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-subtitle {
  font-size: 16px;
  opacity: 0.9;
  color: var(--theme-text-base);
  margin-bottom: 32px;
  line-height: 1.5;
}

.logo-features {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 32px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  opacity: 0.8;
}

.feature-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.feature-item span {
  font-size: 12px;
  color: var(--theme-text-base);
}

.login-header {
  text-align: left;
  margin-bottom: 32px;
}

.login-header h2 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--theme-text-base);
  line-height: 1.2;
}

.login-header p {
  color: var(--theme-text-secondary);
  font-size: 14px;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.login-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.login-mode-buttons {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.mode-btn {
  flex: 1;
  font-size: 14px;
}

.footer-terms {
  text-align: center;
  font-size: 12px;
  line-height: 1.5;
  margin-top: auto;
  padding-top: 24px;
}

.footer-terms a {
  color: var(--theme-primary);
  text-decoration: underline;
}

.footer-terms a:hover {
  color: var(--theme-primary-hover);
}

/* 微信登录模态框样式 */
.wechat-qr-container {
  text-align: center;
  padding: 24px;
}

.qr-code-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 280px;
}

.qr-code-box {
  text-align: center;
}

.qr-code-icon {
  margin-bottom: 24px;
  padding: 24px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  background: var();
}

.qr-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-base);
  margin: 16px 0 8px 0;
}

.qr-tip {
  font-size: 14px;
  color: var(--theme-text-secondary);
  margin: 0;
}

/* 验证码输入组合样式 */
.ant-input-group {
  display: flex;
}

.ant-input-group .ant-input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.ant-input-group .ant-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
  font-size: 14px;
}

/* 页面进入动画 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 卡片淡入动画 */
.card-fade-enter-active {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card-fade-enter-from {
  transform: scale(0.95) translateY(20px);
  opacity: 0;
}

.card-fade-enter-to {
  transform: scale(1) translateY(0);
  opacity: 1;
}

/* Logo内容动画 */
.logo-content > * {
  animation: fadeInUp 0.6s ease-out both;
}

.logo-icon {
  animation-delay: 0.6s;
}

.logo-title {
  animation-delay: 0.8s;
}

.logo-subtitle {
  animation-delay: 1s;
}

.logo-features {
  animation-delay: 1.2s;
}

@keyframes fadeInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-page {
    padding: 10px;
  }

  .login-container {
    flex-direction: column;
    max-width: 500px;
    min-height: auto;
  }

  .logo-section {
    order: -1;
    flex: 0 0 auto;
    min-height: 200px;
  }

  .login-form-section {
    padding: 32px 24px;
  }

  .logo-content {
    padding: 24px;
  }

  .logo-title {
    font-size: 24px;
  }

  .logo-subtitle {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .logo-features {
    gap: 16px;
    margin-top: 20px;
  }

  .feature-item span {
    font-size: 11px;
  }

  .feature-icon {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .login-page {
    padding: 5px;
  }

  .login-container {
    border-radius: 12px;
  }

  .login-form-section {
    padding: 24px 20px;
  }

  .login-header h2 {
    font-size: 24px;
  }

  .login-mode-buttons {
    flex-direction: column;
  }

  .mode-btn {
    width: 100%;
  }

  .ant-input-group .ant-btn {
    width: 100px;
    font-size: 12px;
  }

  .logo-section {
    min-height: 180px;
  }

  .logo-title {
    font-size: 20px;
  }

  .logo-subtitle {
    font-size: 13px;
  }

  .logo-features {
    gap: 12px;
    margin-top: 16px;
  }
}

/* 主题颜色演示样式 */
.color-demo {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: inline-block;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.color-demo:hover {
  transform: scale(1.1);
}
</style>
