/**
 * 角色管理API
 */

import request from '@/utils/request';
import { ROLE_API } from '@/constants/api';
import type {
  PageResult,
  RoleRecord,
  RoleQueryParams,
  RoleFormData,
  RoleDetailRecord
} from '@/types';

/**
 * 角色管理API服务
 */
export class RoleApi {
  /**
   * 获取角色列表
   * @returns 角色列表
   */
  static async getRoleList(): Promise<RoleRecord[]> {
    const response = await request.get<RoleRecord[]>(
      ROLE_API.LIST,
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 分页查询角色
   * @param params 查询参数
   * @returns 分页角色数据
   */
  static async getRolePage(params: RoleQueryParams): Promise<PageResult<RoleDetailRecord>> {
    const response = await request.get<PageResult<RoleDetailRecord>>(
      ROLE_API.PAGE,
      params,
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 根据ID获取角色详情
   * @param id 角色ID
   * @returns 角色详情
   */
  static async getRoleById(id: number): Promise<RoleDetailRecord> {
    const response = await request.get<RoleDetailRecord>(
      ROLE_API.DETAIL(id),
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 新增角色
   * @param roleData 角色数据
   * @returns 创建结果
   */
  static async createRole(roleData: RoleFormData): Promise<RoleDetailRecord> {
    const response = await request.post<RoleDetailRecord>(
      ROLE_API.CREATE,
      roleData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '角色创建成功',
      }
    );

    return response.data;
  }

  /**
   * 修改角色
   * @param id 角色ID
   * @param roleData 角色数据
   * @returns 更新结果
   */
  static async updateRole(id: number, roleData: Partial<RoleFormData>): Promise<RoleDetailRecord> {
    const response = await request.put<RoleDetailRecord>(
      ROLE_API.UPDATE(id),
      roleData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '角色更新成功',
      }
    );

    return response.data;
  }

  /**
   * 删除角色
   * @param id 角色ID
   * @returns 删除结果
   */
  static async deleteRole(id: number): Promise<void> {
    await request.delete(
      ROLE_API.DELETE(id),
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '角色删除成功',
      }
    );
  }
  
  /**
   * 获取角色权限菜单
   * @param id 角色ID
   * @returns 菜单ID数组
   */
  static async getRoleMenus(id: number): Promise<number[]> {
    const response = await request.get<number[]>(
      ROLE_API.MENUS(id),
      {},
      {
        loading: false,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 设置角色权限菜单
   * @param id 角色ID
   * @param menuIds 菜单ID数组
   * @returns 设置结果
   */
  static async setRoleMenus(id: number, menuIds: number[]): Promise<void> {
    await request.post(
      ROLE_API.SET_MENUS(id),
      { menuIds },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '权限设置成功',
      }
    );
  }
  
  /**
   * 复制角色
   * @param id 源角色ID
   * @param newRoleName 新角色名称
   * @param newRoleKey 新角色标识
   * @returns 复制结果
   */
  static async copyRole(id: number, newRoleName: string, newRoleKey: string): Promise<RoleDetailRecord> {
    const response = await request.post<RoleDetailRecord>(
      ROLE_API.COPY(id),
      {
        name: newRoleName,
        key: newRoleKey,
      },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '角色复制成功',
      }
    );

    return response.data;
  }
  
  /**
   * 启用/禁用角色
   * @param id 角色ID
   * @param status 状态 (1-启用 0-禁用)
   * @returns 操作结果
   */
  static async toggleRoleStatus(id: number, status: number): Promise<void> {
    await request.post(
      ROLE_API.TOGGLE_STATUS(id),
      { status },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: status === 1 ? '角色已启用' : '角色已禁用',
      }
    );
  }
}

// 导出默认实例
export default RoleApi;
