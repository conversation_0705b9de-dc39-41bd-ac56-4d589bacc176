/**
 * 客户管理API
 */

import request from '@/utils/request';
import { CUSTOMER_API } from '@/constants/api';
import type {
  PageResult,
  CustomerRecord,
  CustomerQueryParams,
  CustomerFormData
} from '@/types';

/**
 * 客户管理API服务
 */
export class CustomerApi {
  /**
   * 分页查询客户
   * @param params 查询参数
   * @returns 分页客户数据
   */
  static async getCustomerPage(params: CustomerQueryParams): Promise<PageResult<CustomerRecord>> {
    const response = await request.get<PageResult<CustomerRecord>>(
      CUSTOMER_API.PAGE,
      params,
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 根据ID获取客户详情
   * @param id 客户ID
   * @returns 客户详情
   */
  static async getCustomerById(id: number): Promise<CustomerRecord> {
    const response = await request.get<CustomerRecord>(
      CUSTOMER_API.DETAIL(id),
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 新增客户
   * @param customerData 客户数据
   * @returns 创建结果
   */
  static async createCustomer(customerData: CustomerFormData): Promise<CustomerRecord> {
    const response = await request.post<CustomerRecord>(
      CUSTOMER_API.CREATE,
      customerData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '客户创建成功',
      }
    );

    return response.data;
  }
  
  /**
   * 更新客户
   * @param id 客户ID
   * @param customerData 客户数据
   * @returns 更新结果
   */
  static async updateCustomer(id: number, customerData: Partial<CustomerFormData>): Promise<CustomerRecord> {
    const response = await request.put<CustomerRecord>(
      CUSTOMER_API.UPDATE(id),
      customerData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '客户更新成功',
      }
    );

    return response.data;
  }
  
  /**
   * 删除客户
   * @param id 客户ID
   * @returns 删除结果
   */
  static async deleteCustomer(id: number): Promise<void> {
    await request.delete(
      CUSTOMER_API.DELETE(id),
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '客户删除成功',
      }
    );
  }
  
  /**
   * 批量删除客户
   * @param ids 客户ID数组
   * @returns 删除结果
   */
  static async batchDeleteCustomers(ids: number[]): Promise<void> {
    await request.post(
      CUSTOMER_API.BATCH_DELETE,
      { ids },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '批量删除成功',
      }
    );
  }
  
  /**
   * 导出客户数据
   * @param params 查询参数
   * @returns 导出文件
   */
  static async exportCustomers(params: CustomerQueryParams): Promise<Blob> {
    const response = await request.getInstance().get(CUSTOMER_API.EXPORT, {
      params,
      responseType: 'blob',
    });

    return response.data;
  }
  
  /**
   * 导入客户数据
   * @param file Excel文件
   * @returns 导入结果
   */
  static async importCustomers(file: File): Promise<{ success: number; failed: number; errors?: string[] }> {
    const response = await request.upload<{ success: number; failed: number; errors?: string[] }>(
      '/customers/import',
      file,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '客户数据导入成功',
      }
    );
    
    return response.data;
  }
  
  /**
   * 获取客户统计信息
   * @returns 统计数据
   */
  static async getCustomerStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    newThisMonth: number;
  }> {
    const response = await request.get<{
      total: number;
      active: number;
      inactive: number;
      newThisMonth: number;
    }>(
      CUSTOMER_API.STATS,
      {},
      {
        loading: false,
        errorMessage: true,
      }
    );
    
    return response.data;
  }
}

// 导出默认实例
export default CustomerApi;
