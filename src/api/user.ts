/**
 * 用户管理API
 */

import request from '@/utils/request';
import { USER_API } from '@/constants/api';
import type {
  PageResult,
  UserLoginRecord,
  UserQueryParams,
  UserFormData,
  UserDetailRecord
} from '@/types';

/**
 * 用户管理API服务
 */
export class UserApi {
  /**
   * 获取用户列表
   * @returns 用户列表
   */
  static async getUserList(): Promise<UserLoginRecord[]> {
    const response = await request.get<UserLoginRecord[]>(
      USER_API.LIST,
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 分页查询用户
   * @param params 查询参数
   * @returns 分页用户数据
   */
  static async getUserPage(params: UserQueryParams): Promise<PageResult<UserLoginRecord>> {
    const response = await request.get<PageResult<UserLoginRecord>>(
      USER_API.PAGE,
      params,
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 根据ID获取用户详情
   * @param id 用户ID
   * @returns 用户详情
   */
  static async getUserById(id: number): Promise<UserLoginRecord> {
    const response = await request.get<UserLoginRecord>(
      USER_API.DETAIL(id),
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 新增用户
   * @param userData 用户数据
   * @returns 创建结果
   */
  static async createUser(userData: UserFormData): Promise<UserLoginRecord> {
    const response = await request.post<UserLoginRecord>(
      USER_API.CREATE,
      userData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '用户创建成功',
      }
    );

    return response.data;
  }
  
  /**
   * 修改用户
   * @param id 用户ID
   * @param userData 用户数据
   * @returns 更新结果
   */
  static async updateUser(id: number, userData: Partial<UserFormData>): Promise<UserLoginRecord> {
    const response = await request.put<UserLoginRecord>(
      USER_API.UPDATE(id),
      userData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '用户更新成功',
      }
    );

    return response.data;
  }

  /**
   * 删除用户
   * @param id 用户ID
   * @returns 删除结果
   */
  static async deleteUser(id: number): Promise<void> {
    await request.delete(
      USER_API.DELETE(id),
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '用户删除成功',
      }
    );
  }
  
  /**
   * 批量删除用户
   * @param ids 用户ID数组
   * @returns 删除结果
   */
  static async batchDeleteUsers(ids: number[]): Promise<void> {
    await request.post(
      USER_API.BATCH_DELETE,
      { ids },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '批量删除成功',
      }
    );
  }

  /**
   * 重置用户密码
   * @param id 用户ID
   * @param newPassword 新密码
   * @returns 重置结果
   */
  static async resetPassword(id: number, newPassword: string): Promise<void> {
    await request.post(
      USER_API.RESET_PASSWORD(id),
      { password: newPassword },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '密码重置成功',
      }
    );
  }

  /**
   * 启用/禁用用户
   * @param id 用户ID
   * @param status 状态 (1-启用 0-禁用)
   * @returns 操作结果
   */
  static async toggleUserStatus(id: number, status: number): Promise<void> {
    await request.post(
      USER_API.TOGGLE_STATUS(id),
      { status },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: status === 1 ? '用户已启用' : '用户已禁用',
      }
    );
  }
}

// 导出默认实例
export default UserApi;
