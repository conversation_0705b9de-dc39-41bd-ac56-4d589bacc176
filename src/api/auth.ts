/**
 * 认证相关API
 */

import request from '@/utils/request';
import { AUTH_API } from '@/constants/api';
import type {
  UserLoginReq,
  AuthRecord,
  UserLoginRecord,
  MenuRecord
} from '@/types';

/**
 * 认证API服务
 */
export class AuthApi {
  /**
   * 用户登录
   * @param loginData 登录参数
   * @returns 登录响应数据
   */
  static async login(loginData: UserLoginReq): Promise<AuthRecord> {
    const response = await request.post<AuthRecord>(
      AUTH_API.LOGIN,
      loginData,
      {
        loading: true,
        errorMessage: true,
        successMessage: false,
        successText: '登录成功AuthApi',
        withToken: false, // 登录接口不需要token
      }
    );

    return response.data;
  }
  
  /**
   * 用户登出
   * @returns 登出结果
   */
  static async logout(): Promise<void> {
    try {
      await request.post(
        AUTH_API.LOGOUT,
        {},
        {
          loading: true,
          errorMessage: true,
          successMessage: true,
          successText: '退出成功',
        }
      );
    } catch (error) {
      // 即使登出接口失败，也要清除本地token
      console.warn('登出接口调用失败:', error);
    }
  }
  
  /**
   * 刷新token
   * @returns 新的认证信息
   */
  static async refreshToken(): Promise<AuthRecord> {
    const response = await request.post<AuthRecord>(
      AUTH_API.REFRESH_TOKEN,
      {},
      {
        loading: false,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  static async getCurrentUser(): Promise<UserLoginRecord> {
    const response = await request.get<UserLoginRecord>(
      AUTH_API.CURRENT_USER,
      {},
      {
        loading: false,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 获取用户菜单权限
   * @returns 菜单列表
   */
  static async getUserMenus(): Promise<MenuRecord[]> {
    const response = await request.get<MenuRecord[]>(
      AUTH_API.USER_MENUS,
      {},
      {
        loading: false,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 修改密码
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @returns 修改结果
   */
  static async changePassword(
    oldPassword: string,
    newPassword: string
  ): Promise<void> {
    await request.post(
      AUTH_API.CHANGE_PASSWORD,
      {
        oldPassword,
        newPassword,
      },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '密码修改成功',
      }
    );
  }

  /**
   * 验证token有效性
   * @returns 是否有效
   */
  static async validateToken(): Promise<boolean> {
    try {
      // 默认先返回有效，因为目前还没有这个接口
      // await request.get(
      //   AUTH_API.VALIDATE_TOKEN,
      //   {},
      //   {
      //     loading: false,
      //     errorMessage: false,
      //   }
      // );
      return true;
    } catch {
      return false;
    }
  }
}

// 导出默认实例
export default AuthApi;
