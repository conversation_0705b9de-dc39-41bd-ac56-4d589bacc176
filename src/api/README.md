# API 封装使用指南

## 📋 概述

这是一个基于 axios 的现代化 API 请求封装，提供了完整的类型支持、错误处理、拦截器和业务逻辑封装。

## 🏗️ 架构设计

```
src/api/
├── index.ts          # 统一导出
├── auth.ts           # 认证相关API
├── user.ts           # 用户管理API
├── customer.ts       # 客户管理API
├── role.ts           # 角色管理API
├── menu.ts           # 菜单管理API
└── README.md         # 使用文档

src/utils/
└── request.ts        # axios封装核心

src/types/
└── api.ts           # API类型定义
```

## 🚀 快速开始

### 1. 基础使用

```typescript
import { AuthApi, request, TokenManager } from '@/api';

// 使用封装的API服务
const authRecord = await AuthApi.login({
  username: 'admin',
  password: '123456'
});

// 使用原始request实例
const response = await request.get('/custom/endpoint');
```

### 2. 认证流程

```typescript
import { AuthApi, TokenManager } from '@/api';

// 登录
const authRecord = await AuthApi.login({
  username: 'admin',
  password: '123456'
});

// 保存token
TokenManager.setToken(authRecord.authInfo.accessToken);

// 后续请求会自动携带token
const userInfo = await AuthApi.getCurrentUser();

// 登出
await AuthApi.logout();
TokenManager.removeToken();
```

## 🔧 核心功能

### 1. 请求配置

```typescript
interface RequestConfig {
  loading?: boolean;        // 是否显示loading
  errorMessage?: boolean;   // 是否显示错误提示
  successMessage?: boolean; // 是否显示成功提示
  successText?: string;     // 自定义成功提示文本
  withToken?: boolean;      // 是否需要token
  retry?: number;           // 重试次数
  timeout?: number;         // 请求超时时间
}
```

### 2. 自动token管理

```typescript
// Token会自动添加到请求头
const response = await request.get('/protected/data');

// 禁用token
const response = await request.get('/public/data', {}, {
  withToken: false
});

// Token管理工具
TokenManager.setToken('your-token');
TokenManager.getToken();
TokenManager.removeToken();
TokenManager.hasToken();
```

### 3. 错误处理

```typescript
// 自动错误提示（默认开启）
await request.post('/api/data', data);

// 禁用错误提示
await request.post('/api/data', data, {
  errorMessage: false
});

// 自定义错误处理
try {
  await request.post('/api/data', data);
} catch (error) {
  // 自定义错误处理逻辑
}
```

### 4. Loading管理

```typescript
// 自动loading（默认开启）
await request.get('/api/data');

// 禁用loading
await request.get('/api/data', {}, {
  loading: false
});
```

### 5. 成功提示

```typescript
// 显示成功提示
await request.post('/api/data', data, {
  successMessage: true,
  successText: '操作成功'
});
```

## 📝 API服务使用

### 认证API

```typescript
import { AuthApi } from '@/api';

// 登录
const authRecord = await AuthApi.login({
  username: 'admin',
  password: '123456'
});

// 获取当前用户
const userInfo = await AuthApi.getCurrentUser();

// 获取用户菜单
const menus = await AuthApi.getUserMenus();

// 修改密码
await AuthApi.changePassword('oldPassword', 'newPassword');

// 登出
await AuthApi.logout();
```

### 用户管理API

```typescript
import { UserApi } from '@/api';

// 分页查询
const pageResult = await UserApi.getUserPage({
  page: 1,
  pageSize: 10,
  username: 'admin'
});

// 创建用户
const newUser = await UserApi.createUser({
  username: 'newuser',
  nickname: '新用户',
  password: '123456'
});

// 更新用户
await UserApi.updateUser(1, {
  nickname: '更新昵称'
});

// 删除用户
await UserApi.deleteUser(1);
```

### 客户管理API

```typescript
import { CustomerApi } from '@/api';

// 分页查询客户
const customers = await CustomerApi.getCustomerPage({
  page: 1,
  pageSize: 20,
  name: '张三'
});

// 导出客户数据
const blob = await CustomerApi.exportCustomers({
  status: 1
});

// 导入客户数据
const result = await CustomerApi.importCustomers(file);
```

## 🎯 最佳实践

### 1. 类型安全

```typescript
// 使用类型定义
import type { UserLoginReq, LoginResponse } from '@/api';

const loginData: UserLoginReq = {
  username: 'admin',
  password: '123456'
};

const response: LoginResponse = await AuthApi.login(loginData);
```

### 2. 错误处理

```typescript
// 统一错误处理
try {
  const result = await UserApi.createUser(userData);
  // 成功处理
} catch (error) {
  // 错误已经被自动处理（显示错误提示）
  // 这里可以添加额外的错误处理逻辑
}
```

### 3. 条件请求

```typescript
// 根据条件决定是否显示loading
const showLoading = isImportantOperation;

await request.post('/api/data', data, {
  loading: showLoading,
  successMessage: true
});
```

### 4. 文件上传

```typescript
// 文件上传
const file = document.querySelector('input[type="file"]').files[0];
const response = await request.upload('/upload', file, {
  loading: true,
  successMessage: true,
  successText: '文件上传成功'
});
```

## 🔧 配置说明

### 环境变量

```bash
# API基础地址
VITE_API_BASE_URL=/api

# 请求超时时间
VITE_REQUEST_TIMEOUT=10000

# Token存储key
VITE_TOKEN_KEY=combo-opm-token
```

### 请求拦截器

- 自动添加token到请求头
- 显示/隐藏loading
- 请求参数预处理

### 响应拦截器

- 统一响应格式处理
- 业务错误码处理
- HTTP状态码处理
- 自动token过期处理
- 成功/错误提示

## 🚨 注意事项

1. **Token管理**: 登录成功后务必调用 `TokenManager.setToken()` 保存token
2. **错误处理**: 大部分错误会自动处理，特殊情况需要自定义处理
3. **Loading状态**: 默认开启loading，可根据需要禁用
4. **类型安全**: 建议使用TypeScript类型定义确保类型安全
5. **环境配置**: 确保环境变量配置正确

## 📚 扩展开发

### 添加新的API服务

1. 在 `src/api/` 目录下创建新的API文件
2. 定义相关类型在 `src/types/api.ts`
3. 在 `src/api/index.ts` 中导出
4. 参考现有API服务的实现模式

### 自定义拦截器

```typescript
// 获取axios实例
const axiosInstance = request.getInstance();

// 添加自定义拦截器
axiosInstance.interceptors.request.use(config => {
  // 自定义请求处理
  return config;
});
```
