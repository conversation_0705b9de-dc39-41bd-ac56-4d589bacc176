/**
 * 菜单管理API
 */

import request from '@/utils/request';
import { MENU_API } from '@/constants/api';
import type {
  PageResult,
  MenuRecord,
  MenuQueryParams,
  MenuFormData,
  MenuDetailRecord
} from '@/types';

/**
 * 菜单管理API服务
 */
export class MenuApi {
  /**
   * 获取菜单列表
   * @returns 菜单列表
   */
  static async getMenuList(): Promise<MenuDetailRecord[]> {
    const response = await request.get<MenuDetailRecord[]>(
      MENU_API.LIST,
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 获取树形菜单列表
   * @returns 树形菜单列表
   */
  static async getMenuTree(): Promise<MenuDetailRecord[]> {
    const response = await request.get<MenuDetailRecord[]>(
      MENU_API.TREE,
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 分页查询菜单
   * @param params 查询参数
   * @returns 分页菜单数据
   */
  static async getMenuPage(params: MenuQueryParams): Promise<PageResult<MenuDetailRecord>> {
    const response = await request.get<PageResult<MenuDetailRecord>>(
      MENU_API.PAGE,
      params,
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }
  
  /**
   * 根据ID获取菜单详情
   * @param id 菜单ID
   * @returns 菜单详情
   */
  static async getMenuById(id: number): Promise<MenuDetailRecord> {
    const response = await request.get<MenuDetailRecord>(
      MENU_API.DETAIL(id),
      {},
      {
        loading: true,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 新增菜单
   * @param menuData 菜单数据
   * @returns 创建结果
   */
  static async createMenu(menuData: MenuFormData): Promise<MenuDetailRecord> {
    const response = await request.post<MenuDetailRecord>(
      MENU_API.CREATE,
      menuData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '菜单创建成功',
      }
    );

    return response.data;
  }
  
  /**
   * 修改菜单
   * @param id 菜单ID
   * @param menuData 菜单数据
   * @returns 更新结果
   */
  static async updateMenu(id: number, menuData: Partial<MenuFormData>): Promise<MenuDetailRecord> {
    const response = await request.put<MenuDetailRecord>(
      MENU_API.UPDATE(id),
      menuData,
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '菜单更新成功',
      }
    );

    return response.data;
  }

  /**
   * 删除菜单
   * @param id 菜单ID
   * @returns 删除结果
   */
  static async deleteMenu(id: number): Promise<void> {
    await request.delete(
      MENU_API.DELETE(id),
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '菜单删除成功',
      }
    );
  }
  
  /**
   * 获取父级菜单选项
   * @param excludeId 排除的菜单ID（编辑时排除自己）
   * @returns 父级菜单选项
   */
  static async getParentMenuOptions(excludeId?: number): Promise<MenuDetailRecord[]> {
    const response = await request.get<MenuDetailRecord[]>(
      MENU_API.PARENT_OPTIONS,
      { excludeId },
      {
        loading: false,
        errorMessage: true,
      }
    );

    return response.data;
  }

  /**
   * 移动菜单位置
   * @param id 菜单ID
   * @param targetId 目标位置菜单ID
   * @param position 位置 ('before' | 'after' | 'inside')
   * @returns 移动结果
   */
  static async moveMenu(
    id: number,
    targetId: number,
    position: 'before' | 'after' | 'inside'
  ): Promise<void> {
    await request.post(
      MENU_API.MOVE(id),
      {
        targetId,
        position,
      },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '菜单移动成功',
      }
    );
  }
  
  /**
   * 批量更新菜单排序
   * @param menuSorts 菜单排序数据
   * @returns 更新结果
   */
  static async batchUpdateSort(menuSorts: Array<{ id: number; sort: number }>): Promise<void> {
    await request.post(
      MENU_API.BATCH_SORT,
      { menuSorts },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: '排序更新成功',
      }
    );
  }

  /**
   * 启用/禁用菜单
   * @param id 菜单ID
   * @param status 状态 (1-启用 0-禁用)
   * @returns 操作结果
   */
  static async toggleMenuStatus(id: number, status: number): Promise<void> {
    await request.post(
      MENU_API.TOGGLE_STATUS(id),
      { status },
      {
        loading: true,
        errorMessage: true,
        successMessage: true,
        successText: status === 1 ? '菜单已启用' : '菜单已禁用',
      }
    );
  }
}

// 导出默认实例
export default MenuApi;
