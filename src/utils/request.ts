/**
 * Axios 请求封装
 * 提供统一的请求配置、拦截器、错误处理
 */

import axios, { 
  type AxiosInstance, 
  type AxiosRequestConfig, 
  type AxiosResponse, 
  type AxiosError,
  type InternalAxiosRequestConfig 
} from 'axios';
import { message } from 'ant-design-vue';
import type {
  ApiResponse,
  RequestConfig,
} from '@/types/api';
import { AuthStorageManager } from '@/utils/storage';
import { PINIA_PERSIST_KEYS } from '@/constants/storage';

// 请求配置常量
const REQUEST_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
} as const;

// Token 管理
class TokenManager {
  static getToken(): string | null {
    // 从 Pinia 持久化存储中读取 token
    try {
      const authStoreData = localStorage.getItem(PINIA_PERSIST_KEYS.AUTH_STORE);
      if (authStoreData) {
        const parsedData = JSON.parse(authStoreData);
        return parsedData.accessToken || null;
      }
    } catch (error) {
      // 静默处理错误，避免控制台污染
    }
    return null;
  }

  /** @deprecated */
  static setToken(token: string): void {
    // 注意：这个方法现在主要由 Pinia store 管理
    // 这里保留是为了兼容性，但实际的持久化由 pinia-plugin-persistedstate 处理
    AuthStorageManager.setAccessToken(token);
  }
  
  /** @deprecated */
  static removeToken(): void {
    // 注意：这个方法现在主要由 Pinia store 管理
    // 这里保留是为了兼容性
    AuthStorageManager.clearAuthData();
  }

  static hasToken(): boolean {
    return !!this.getToken();
  }
}

// Loading 管理
class LoadingManager {
  private static loadingCount = 0;
  
  static show(): void {
    if (this.loadingCount === 0) {
      // 这里可以集成你的全局loading组件
      console.log('显示全局loading');
    }
    this.loadingCount++;
  }
  
  static hide(): void {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    if (this.loadingCount === 0) {
      // 隐藏全局loading
      console.log('隐藏全局loading');
    }
  }
  
  static clear(): void {
    this.loadingCount = 0;
    console.log('清除所有loading');
  }
}

// HTTP请求类
class HttpRequest {
  private instance: AxiosInstance;
  
  constructor() {
    this.instance = axios.create({
      baseURL: REQUEST_CONFIG.BASE_URL,
      timeout: REQUEST_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    this.setupInterceptors();
  }
  
  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        const customConfig = config as InternalAxiosRequestConfig & RequestConfig;
        
        // 添加token
        if (customConfig.withToken !== false) {
          const token = TokenManager.getToken();
          if (token) {
            config.headers['combo-opm-token'] = token;
          }
        }
        
        // 显示loading
        if (customConfig.loading !== false) {
          LoadingManager.show();
        }
        
        return config;
      },
      (error: AxiosError) => {
        LoadingManager.hide();
        return Promise.reject(error);
      }
    );
    
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const customConfig = response.config as AxiosRequestConfig & RequestConfig;
        
        // 隐藏loading
        if (customConfig.loading !== false) {
          LoadingManager.hide();
        }
        
        const { data } = response;
        
        // 处理业务逻辑
        if (this.isApiResponse(data)) {
          // 成功提示
          if (customConfig.successMessage && data.code === 0) {
            // 有传入成功提示文本才提示
            if (customConfig.successMessage) { 
              message.success(customConfig.successText);
            }
          }
          
          // 业务错误处理
          if (data.code !== 0) {
            this.handleBusinessError(data, customConfig);
            return Promise.reject(new Error(data.message || '请求失败'));
          }
          
          return data;
        }
        
        return data;
      },
      (error: AxiosError) => {
        const customConfig = error.config as AxiosRequestConfig & RequestConfig;
        
        // 隐藏loading
        if (customConfig?.loading !== false) {
          LoadingManager.hide();
        }
        
        return this.handleHttpError(error, customConfig);
      }
    );
  }
  
  /**
   * 判断是否为API响应格式
   */
  private isApiResponse(data: any): data is ApiResponse {
    return data && typeof data.code === 'number';
  }
  
  /**
   * 处理业务错误
   */
  private handleBusinessError(data: ApiResponse, config?: RequestConfig): void {
    if (config?.errorMessage === false) return;
    
    switch (data.code) {
      case 401:
        message.error('登录已过期，请重新登录');
        TokenManager.removeToken();
        // 跳转到登录页
        window.location.href = '/login';
        break;
      case 403:
        message.error('没有权限访问该资源');
        break;
      default:
        message.error(data.message || '请求失败');
    }
  }
  
  /**
   * 处理HTTP错误
   */
  private handleHttpError(error: AxiosError, config?: RequestConfig): Promise<never> {
    if (config?.errorMessage === false) {
      return Promise.reject(error);
    }
    
    let errorMessage = '网络请求失败';
    
    if (error.response) {
      const { status } = error.response;
      switch (status) {
        case 400:
          errorMessage = '请求参数错误';
          break;
        case 401:
          errorMessage = '登录已过期，请重新登录';
          TokenManager.removeToken();
          window.location.href = '/login';
          break;
        case 403:
          errorMessage = '没有权限访问';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        case 502:
          errorMessage = '网关错误';
          break;
        case 503:
          errorMessage = '服务暂不可用';
          break;
        default:
          errorMessage = `请求失败 (${status})`;
      }
    } else if (error.request) {
      errorMessage = '网络连接失败，请检查网络';
    }
    
    message.error(errorMessage);
    return Promise.reject(error);
  }
  
  /**
   * GET请求
   */
  get<T = any>(
    url: string, 
    params?: any, 
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.get(url, { 
      params, 
      ...config 
    });
  }
  
  /**
   * POST请求
   */
  post<T = any>(
    url: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, config);
  }
  
  /**
   * PUT请求
   */
  put<T = any>(
    url: string, 
    data?: any, 
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.put(url, data, config);
  }
  
  /**
   * DELETE请求
   */
  delete<T = any>(
    url: string, 
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    return this.instance.delete(url, config);
  }
  
  /**
   * 上传文件
   */
  upload<T = any>(
    url: string, 
    file: File | FormData, 
    config?: RequestConfig
  ): Promise<ApiResponse<T>> {
    const formData = file instanceof FormData ? file : new FormData();
    if (file instanceof File) {
      formData.append('file', file);
    }
    
    return this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    });
  }
  
  /**
   * 获取原始axios实例
   */
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

// 创建请求实例
const request = new HttpRequest();

// 导出请求方法和工具
export default request;
export { TokenManager, LoadingManager };
export type { RequestConfig };
