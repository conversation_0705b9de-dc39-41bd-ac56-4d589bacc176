/**
 * 本地存储工具类
 * 提供统一的localStorage和sessionStorage操作接口
 */

import { AUTH_STORAGE_KEYS, STORAGE_EXPIRES } from '@/constants/storage';

// ===== 存储数据接口 =====
interface StorageData<T = any> {
  value: T;
  expires?: number; // 过期时间戳，-1表示永不过期
  timestamp: number; // 存储时间戳
}

/**
 * 本地存储管理类
 */
export class StorageManager {
  /**
   * 设置localStorage数据
   * @param key 存储键
   * @param value 存储值
   * @param expires 过期时间（毫秒），-1表示永不过期
   */
  static setItem<T>(key: string, value: T, expires?: number): void {
    try {
      const data: StorageData<T> = {
        value,
        expires: expires ? Date.now() + expires : -1,
        timestamp: Date.now(),
      };
      
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error('localStorage设置失败:', error);
    }
  }

  /**
   * 获取localStorage数据
   * @param key 存储键
   * @param defaultValue 默认值
   * @returns 存储的值或默认值
   */
  static getItem<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key);
      if (!item) return defaultValue || null;

      const data: StorageData<T> = JSON.parse(item);
      
      // 检查是否过期
      if (data.expires !== -1 && data.expires && data.expires < Date.now()) {
        localStorage.removeItem(key);
        return defaultValue || null;
      }

      return data.value;
    } catch (error) {
      console.error('localStorage获取失败:', error);
      return defaultValue || null;
    }
  }

  /**
   * 移除localStorage数据
   * @param key 存储键
   */
  static removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('localStorage移除失败:', error);
    }
  }

  /**
   * 清空localStorage
   */
  static clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('localStorage清空失败:', error);
    }
  }

  /**
   * 检查localStorage是否可用
   */
  static isAvailable(): boolean {
    try {
      const testKey = '__storage_test__';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取localStorage使用情况
   */
  static getStorageInfo(): { used: number; total: number; available: number } {
    let used = 0;
    const total = 5 * 1024 * 1024; // 5MB (大概值)

    try {
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }
    } catch (error) {
      console.error('获取存储信息失败:', error);
    }

    return {
      used,
      total,
      available: total - used,
    };
  }
}

/**
 * 认证相关存储管理类
 */
export class AuthStorageManager {
  /**
   * 设置访问令牌
   * @param token 访问令牌
   * @param expires 过期时间（毫秒）
   */
  static setAccessToken(token: string, expires?: number): void {
    StorageManager.setItem(
      AUTH_STORAGE_KEYS.ACCESS_TOKEN, 
      token, 
      expires || STORAGE_EXPIRES.TOKEN_DEFAULT
    );
  }

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return StorageManager.getItem<string>(AUTH_STORAGE_KEYS.ACCESS_TOKEN);
  }

  /**
   * 设置刷新令牌
   * @param token 刷新令牌
   */
  static setRefreshToken(token: string): void {
    StorageManager.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, token, STORAGE_EXPIRES.TOKEN_DEFAULT);
  }

  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | null {
    return StorageManager.getItem<string>(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
  }

  /**
   * 设置用户信息
   * @param userInfo 用户信息
   */
  static setUserInfo(userInfo: any): void {
    StorageManager.setItem(AUTH_STORAGE_KEYS.USER_INFO, userInfo, -1);
  }

  /**
   * 获取用户信息
   */
  static getUserInfo<T = any>(): T | null {
    return StorageManager.getItem<T>(AUTH_STORAGE_KEYS.USER_INFO);
  }

  /**
   * 设置用户菜单权限
   * @param menus 菜单列表
   */
  static setUserMenus(menus: any[]): void {
    StorageManager.setItem(AUTH_STORAGE_KEYS.USER_MENUS, menus, -1);
  }

  /**
   * 获取用户菜单权限
   */
  static getUserMenus<T = any[]>(): T | null {
    return StorageManager.getItem<T>(AUTH_STORAGE_KEYS.USER_MENUS);
  }

  /**
   * 设置用户权限列表
   * @param permissions 权限列表
   */
  static setUserPermissions(permissions: string[]): void {
    StorageManager.setItem(AUTH_STORAGE_KEYS.USER_PERMISSIONS, permissions, -1);
  }

  /**
   * 获取用户权限列表
   */
  static getUserPermissions(): string[] {
    return StorageManager.getItem<string[]>(AUTH_STORAGE_KEYS.USER_PERMISSIONS, []) || [];
  }

  /**
   * 设置用户角色列表
   * @param roles 角色列表
   */
  static setUserRoles(roles: string[]): void {
    StorageManager.setItem(AUTH_STORAGE_KEYS.USER_ROLES, roles, -1);
  }

  /**
   * 获取用户角色列表
   */
  static getUserRoles(): string[] {
    return StorageManager.getItem<string[]>(AUTH_STORAGE_KEYS.USER_ROLES, []) || [];
  }

  /**
   * 设置登录状态
   * @param status 登录状态
   */
  static setLoginStatus(status: string): void {
    StorageManager.setItem(AUTH_STORAGE_KEYS.LOGIN_STATUS, status, -1);
  }

  /**
   * 获取登录状态
   */
  static getLoginStatus(): string | null {
    return StorageManager.getItem<string>(AUTH_STORAGE_KEYS.LOGIN_STATUS);
  }

  /**
   * 清除所有认证相关数据
   */
  static clearAuthData(): void {
    Object.values(AUTH_STORAGE_KEYS).forEach(key => {
      StorageManager.removeItem(key);
    });
  }

  /**
   * 检查Token是否存在且未过期
   */
  static isTokenValid(): boolean {
    const token = this.getAccessToken();
    return !!token;
  }
}

// 导出默认实例
export default StorageManager;
