/**
 * 菜单工具函数
 * 用于处理菜单数据转换和操作
 */

import { h } from 'vue';
import type { MenuRecord } from '@/types/auth';
import type { AntdMenuItemType, MenuTransformConfig } from '@/types/menu';
import { MenuType } from '@/types/auth';

// 导入 Ant Design Vue 图标
import {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  SettingOutlined,
  MoneyCollectOutlined,
  LineChartOutlined,
  BarChartOutlined,
  ShoppingCartOutlined,
  FileTextOutlined,
  BellOutlined,
  SafetyOutlined,
  DatabaseOutlined,
  CloudOutlined,
  MobileOutlined,
  DesktopOutlined,
  PrinterOutlined,
  CameraOutlined,
  HeartOutlined,
  StarOutlined,
  HomeOutlined,
  AppstoreOutlined,
  MenuOutlined,
  FolderOutlined,
  FileOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  ShareAltOutlined,
  CopyOutlined,
  ScissorOutlined,
  HighlightOutlined,
  FontSizeOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  RedoOutlined,
  UndoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  CompressOutlined,
  ExpandOutlined,
  QuestionCircleOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  StopOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  ForwardOutlined,
  BackwardOutlined,
  CaretRightOutlined,
  CaretLeftOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  UpOutlined,
  DownOutlined,
  LeftOutlined,
  RightOutlined,
  DoubleRightOutlined,
  DoubleLeftOutlined,
  VerticalRightOutlined,
  VerticalLeftOutlined,
  VerticalAlignTopOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignBottomOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  BorderOutlined,
  BorderlessTableOutlined,
  TableOutlined,
  InsertRowAboveOutlined,
  InsertRowBelowOutlined,
  InsertRowLeftOutlined,
  InsertRowRightOutlined,
  DeleteRowOutlined,
  DeleteColumnOutlined,
  MergeCellsOutlined,
  SplitCellsOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  HistoryOutlined,
  NodeIndexOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  BarsOutlined,
  BlockOutlined,
  ExpandAltOutlined,
  ShrinkOutlined,
  ArrowsAltOutlined,
  SendOutlined,
  PaperClipOutlined,
  LinkOutlined,
  ScanOutlined,
  QrcodeOutlined,
  BarcodeOutlined,
  RadarChartOutlined,
  AreaChartOutlined,
  PieChartOutlined,
  FundOutlined,
  SlackOutlined,
  BehanceOutlined,
  DribbbleOutlined,
  InstagramOutlined,
  YuqueOutlined,
  AlibabaOutlined,
  YahooOutlined,
  RedditOutlined,
  SkypeOutlined,
  CodeSandboxOutlined,
  ChromeOutlined,
  DeploymentUnitOutlined,
  AccountBookOutlined,
  AlertOutlined,
  ApartmentOutlined,
  ApiOutlined,
  AppstoreAddOutlined,
  AudioOutlined,
  BankOutlined,
  BgColorsOutlined,
  BookOutlined,
  BranchesOutlined,
  BugOutlined,
  BuildOutlined,
  BulbOutlined,
  CalculatorOutlined,
  CarOutlined,
  CarryOutOutlined,
  ClearOutlined,
  CloudDownloadOutlined,
  CloudServerOutlined,
  CloudSyncOutlined,
  CloudUploadOutlined,
  ClusterOutlined,
  CodeOutlined,
  CoffeeOutlined,
  CommentOutlined,
  CompassOutlined,
  ContactsOutlined,
  ContainerOutlined,
  ControlOutlined,
  CopyrightOutlined,
  CreditCardOutlined,
  CrownOutlined,
  CustomerServiceOutlined,
  DashboardFilled,
  DisconnectOutlined,
  DislikeOutlined,
  DollarCircleOutlined,
  DollarOutlined,
  EnvironmentOutlined,
  EuroCircleOutlined,
  EuroOutlined,
  ExceptionOutlined,
  ExperimentOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  FieldTimeOutlined,
  FileAddOutlined,
  FileDoneOutlined,
  FileExcelOutlined,
  FileExclamationOutlined,
  FileImageOutlined,
  FileJpgOutlined,
  FileMarkdownOutlined,
  FilePdfOutlined,
  FilePptOutlined,
  FileProtectOutlined,
  FileSearchOutlined,
  FileSyncOutlined,
  FileUnknownOutlined,
  FileWordOutlined,
  FileZipOutlined,
  FireOutlined,
  FlagOutlined,
  FolderAddOutlined,
  FolderOpenOutlined,
  ForkOutlined,
  FormatPainterOutlined,
  FunctionOutlined,
  FundProjectionScreenOutlined,
  FunnelPlotOutlined,
  GatewayOutlined,
  GiftOutlined,
  GlobalOutlined,
  GoldOutlined,
  GroupOutlined,
  HddOutlined,
  HeatMapOutlined,
  HighlightFilled,
  HolderOutlined,
  IdcardOutlined,
  ImportOutlined,
  InboxOutlined,
  InsuranceOutlined,
  InteractionOutlined,
  KeyOutlined,
  LaptopOutlined,
  LayoutOutlined,
  LikeOutlined,
  LineOutlined,
  LoadingOutlined,
  LockOutlined,
  MailOutlined,
  ManOutlined,
  MedicineBoxOutlined,
  MehOutlined,
  MessageOutlined,
  MinusCircleOutlined,
  MinusOutlined,
  MonitorOutlined,
  MoreOutlined,
  NodeCollapseOutlined,
  NodeExpandOutlined,
  NotificationOutlined,
  NumberOutlined,
  PartitionOutlined,
  PayCircleOutlined,
  PercentageOutlined,
  PhoneOutlined,
  PictureOutlined,
  PieChartFilled,
  PlaySquareOutlined,
  PlusCircleOutlined,
  PoundCircleOutlined,
  PoundOutlined,
  PoweroffOutlined,
  ProfileOutlined,
  ProjectOutlined,
  PropertySafetyOutlined,
  PullRequestOutlined,
  PushpinOutlined,
  ReadOutlined,
  ReconciliationOutlined,
  RedEnvelopeOutlined,
  RestOutlined,
  RobotOutlined,
  RocketOutlined,
  SafetyCertificateOutlined,
  ScheduleOutlined,
  SecurityScanOutlined,
  SelectOutlined,
  ShakeOutlined,
  ShopOutlined,
  ShoppingOutlined,
  SisternodeOutlined,
  SkinOutlined,
  SmileOutlined,
  SolutionOutlined,
  SoundOutlined,
  StockOutlined,
  SwitcherOutlined,
  TabletOutlined,
  TagOutlined,
  TagsOutlined,
  TaobaoCircleOutlined,
  TaobaoOutlined,
  ThunderboltOutlined,
  ToTopOutlined,
  TrademarkCircleOutlined,
  TrademarkOutlined,
  TransactionOutlined,
  TrophyOutlined,
  UngroupOutlined,
  UnlockOutlined,
  UsergroupAddOutlined,
  UsergroupDeleteOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  UserSwitchOutlined,
  VideoCameraOutlined,
  WalletOutlined,
  WifiOutlined,
  BorderInnerOutlined,
  BorderTopOutlined,
  BorderBottomOutlined,
  BorderLeftOutlined,
  BorderRightOutlined,
  PicCenterOutlined,
  PicLeftOutlined,
  PicRightOutlined,
  RadiusBottomleftOutlined,
  RadiusBottomrightOutlined,
  RadiusUpleftOutlined,
  RadiusUprightOutlined,
  AntDesignOutlined,
  AntCloudOutlined,
  FormatPainterFilled,
  MacCommandOutlined,
  WindowsOutlined,
  QqOutlined,
  WechatOutlined,
  IeOutlined,
  AndroidOutlined,
  AppleOutlined,
  Html5Outlined,
  WhatsAppOutlined,
} from '@ant-design/icons-vue';

/**
 * 图标映射表
 * 将字符串图标名称映射到对应的 Vue 组件
 */
const ICON_MAP: Record<string, any> = {
  // 基础图标
  'dashboard': DashboardOutlined,
  'user': UserOutlined,
  'team': TeamOutlined,
  'setting': SettingOutlined,
  'money-collect': MoneyCollectOutlined,
  'line-chart': LineChartOutlined,
  'bar-chart': BarChartOutlined,
  'shopping-cart': ShoppingCartOutlined,
  'file-text': FileTextOutlined,
  'bell': BellOutlined,
  'safety': SafetyOutlined,
  'database': DatabaseOutlined,
  'cloud': CloudOutlined,
  'mobile': MobileOutlined,
  'desktop': DesktopOutlined,
  'printer': PrinterOutlined,
  'camera': CameraOutlined,
  'heart': HeartOutlined,
  'star': StarOutlined,
  'home': HomeOutlined,
  'appstore': AppstoreOutlined,
  'menu': MenuOutlined,
  'folder': FolderOutlined,
  'file': FileOutlined,
  
  // 操作图标
  'edit': EditOutlined,
  'delete': DeleteOutlined,
  'plus': PlusOutlined,
  'search': SearchOutlined,
  'filter': FilterOutlined,
  'reload': ReloadOutlined,
  'download': DownloadOutlined,
  'upload': UploadOutlined,
  'share-alt': ShareAltOutlined,
  'copy': CopyOutlined,
  'scissor': ScissorOutlined,
  
  // 状态图标
  'question-circle': QuestionCircleOutlined,
  'info-circle': InfoCircleOutlined,
  'exclamation-circle': ExclamationCircleOutlined,
  'check-circle': CheckCircleOutlined,
  'close-circle': CloseCircleOutlined,
  'warning': WarningOutlined,
  
  // 方向图标
  'up': UpOutlined,
  'down': DownOutlined,
  'left': LeftOutlined,
  'right': RightOutlined,
  'double-right': DoubleRightOutlined,
  'double-left': DoubleLeftOutlined,
  
  // 业务图标
  'calendar': CalendarOutlined,
  'clock-circle': ClockCircleOutlined,
  'history': HistoryOutlined,
  'wallet': WalletOutlined,
  'credit-card': CreditCardOutlined,
  'bank': BankOutlined,
  'dollar': DollarOutlined,
  'euro': EuroOutlined,
  'pound': PoundOutlined,
  'transaction': TransactionOutlined,
  'account-book': AccountBookOutlined,
  'fund': FundOutlined,
  'stock': StockOutlined,
  'pie-chart': PieChartOutlined,
  'area-chart': AreaChartOutlined,
  'radar-chart': RadarChartOutlined,
  'menu-unfold': MenuUnfoldOutlined,
  'menu-fold': MenuFoldOutlined,
  
  // 用户相关
  'user-add': UserAddOutlined,
  'user-delete': UserDeleteOutlined,
  'user-switch': UserSwitchOutlined,
  'usergroup-add': UsergroupAddOutlined,
  'usergroup-delete': UsergroupDeleteOutlined,
  'contacts': ContactsOutlined,
  'idcard': IdcardOutlined,
  'man': ManOutlined,
  'crown': CrownOutlined,
  'customer-service': CustomerServiceOutlined,
  
  // 系统相关
  'api': ApiOutlined,
  'code': CodeOutlined,
  'bug': BugOutlined,
  'build': BuildOutlined,
  'deployment-unit': DeploymentUnitOutlined,
  'cluster': ClusterOutlined,
  'gateway': GatewayOutlined,
  'hdd': HddOutlined,
  'monitor': MonitorOutlined,
  'laptop': LaptopOutlined,
  'tablet': TabletOutlined,
  'wifi': WifiOutlined,
  'disconnect': DisconnectOutlined,
  'security-scan': SecurityScanOutlined,
  'safety-certificate': SafetyCertificateOutlined,
  'lock': LockOutlined,
  'unlock': UnlockOutlined,
  'key': KeyOutlined,
  
  // 文件相关
  'file-add': FileAddOutlined,
  'file-excel': FileExcelOutlined,
  'file-word': FileWordOutlined,
  'file-pdf': FilePdfOutlined,
  'file-image': FileImageOutlined,
  'file-zip': FileZipOutlined,
  'folder-add': FolderAddOutlined,
  'folder-open': FolderOpenOutlined,
  'inbox': InboxOutlined,
  'container': ContainerOutlined,
  
  // 通信相关
  'mail': MailOutlined,
  'message': MessageOutlined,
  'comment': CommentOutlined,
  'phone': PhoneOutlined,
  'notification': NotificationOutlined,
  'sound': SoundOutlined,
  'video-camera': VideoCameraOutlined,
  'whatsapp': WhatsAppOutlined,
  
  // 商业相关
  'shop': ShopOutlined,
  'shopping': ShoppingOutlined,
  'gift': GiftOutlined,
  'red-envelope': RedEnvelopeOutlined,
  'trophy': TrophyOutlined,
  'medicine-box': MedicineBoxOutlined,
  'car': CarOutlined,
  'rocket': RocketOutlined,
  'fire': FireOutlined,
  'thunderbolt': ThunderboltOutlined,
  'bulb': BulbOutlined,
  'experiment': ExperimentOutlined,
  'compass': CompassOutlined,
  'global': GlobalOutlined,
  'environment': EnvironmentOutlined,
  'slack': SlackOutlined,
  'audit': AudioOutlined,
  
  // 默认图标
  'default': FileOutlined,
};

/**
 * 获取图标组件
 * @param iconName 图标名称
 * @returns Vue 图标组件
 */
export const getIconComponent = (iconName?: string): any => {
  if (!iconName) {
    return ICON_MAP['default'];
  }
  
  // 处理不同的图标名称格式
  const normalizedName = iconName
    .toLowerCase()
    .replace(/outlined$/, '') // 移除 Outlined 后缀
    .replace(/filled$/, '')   // 移除 Filled 后缀
    .replace(/_/g, '-')       // 下划线转横线
    .replace(/([A-Z])/g, '-$1') // 驼峰转横线
    .replace(/^-/, '')        // 移除开头的横线
    .toLowerCase();
  
  return ICON_MAP[normalizedName] || ICON_MAP['default'];
};

/**
 * 默认菜单转换配置
 */
const DEFAULT_TRANSFORM_CONFIG: Required<MenuTransformConfig> = {
  showIcon: true,
  filterHidden: true,
  filterButtons: true,
  iconMapper: getIconComponent,
  labelGenerator: (menu: MenuRecord) => menu.title || menu.name,
};

/**
 * 将 MenuRecord 转换为 Ant Design Vue 菜单格式
 * @param menus 菜单记录列表
 * @param config 转换配置
 * @returns Ant Design Vue 菜单项列表
 */
export const transformMenusToAntd = (
  menus: MenuRecord[],
  config: Partial<MenuTransformConfig> = {}
): AntdMenuItemType[] => {
  const finalConfig = { ...DEFAULT_TRANSFORM_CONFIG, ...config };
  
  const transformMenu = (menu: MenuRecord): AntdMenuItemType | null => {
    // 过滤按钮类型菜单
    if (finalConfig.filterButtons && menu.type === MenuType.BUTTON) {
      return null;
    }
    
    // 过滤隐藏菜单（如果菜单有 visible 属性）
    if (finalConfig.filterHidden && 'visible' in menu && !(menu as any).visible) {
      return null;
    }
    
    // 生成菜单项的基础属性
    const baseItem: Partial<AntdMenuItemType> = {
      key: menu.id.toString(),
      label: finalConfig.labelGenerator(menu),
    };
    
    // 添加图标
    if (finalConfig.showIcon && menu.icon) {
      const IconComponent = finalConfig.iconMapper(menu.icon);
      if (IconComponent) {
        baseItem.icon = () => h(IconComponent);
      }
    }
    
    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      const children = menu.children
        .map(transformMenu)
        .filter((item): item is AntdMenuItemType => item !== null);
      
      if (children.length > 0) {
        return {
          ...baseItem,
          children,
        } as AntdMenuItemType;
      }
    }
    
    // 返回普通菜单项
    return baseItem as AntdMenuItemType;
  };
  
  return menus
    .map(transformMenu)
    .filter((item): item is AntdMenuItemType => item !== null);
};

/**
 * 从菜单中提取所有菜单键
 * @param menus 菜单列表
 * @returns 菜单键数组
 */
export const extractMenuKeys = (menus: MenuRecord[]): string[] => {
  const keys: string[] = [];
  
  const extractFromMenu = (menu: MenuRecord) => {
    keys.push(menu.id.toString());
    if (menu.children && menu.children.length > 0) {
      menu.children.forEach(extractFromMenu);
    }
  };
  
  menus.forEach(extractFromMenu);
  return keys;
};

/**
 * 根据路径查找对应的菜单键
 * @param menus 菜单列表
 * @param path 路径
 * @returns 菜单键
 */
export const findMenuKeyByPath = (menus: MenuRecord[], path: string): string | null => {
  const findInMenu = (menu: MenuRecord): string | null => {
    if (menu.path === path) {
      return menu.id.toString();
    }
    
    if (menu.children && menu.children.length > 0) {
      for (const child of menu.children) {
        const result = findInMenu(child);
        if (result) {
          return result;
        }
      }
    }
    
    return null;
  };
  
  for (const menu of menus) {
    const result = findInMenu(menu);
    if (result) {
      return result;
    }
  }
  
  return null;
};

/**
 * 获取菜单的所有父级键
 * @param menus 菜单列表
 * @param targetKey 目标菜单键
 * @returns 父级键数组
 */
export const getMenuParentKeys = (menus: MenuRecord[], targetKey: string): string[] => {
  const parentKeys: string[] = [];
  
  const findParents = (menuList: MenuRecord[], parents: string[] = []): boolean => {
    for (const menu of menuList) {
      const currentPath = [...parents, menu.id.toString()];
      
      if (menu.id.toString() === targetKey) {
        parentKeys.push(...parents);
        return true;
      }
      
      if (menu.children && menu.children.length > 0) {
        if (findParents(menu.children, currentPath)) {
          return true;
        }
      }
    }
    return false;
  };
  
  findParents(menus);
  return parentKeys;
};
