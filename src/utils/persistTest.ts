/**
 * Pinia 持久化测试工具
 */

import { useAuthStore } from '@/stores/modules/auth';
import { LoginStatus } from '@/types/auth';

/**
 * 测试持久化功能
 */
export function testPersistence() {
  console.log('🧪 开始测试 Pinia 持久化功能...');
  
  const authStore = useAuthStore();
  
  // 1. 检查当前状态
  console.log('📊 当前状态:');
  console.log('  - loginStatus:', authStore.loginStatus);
  console.log('  - accessToken:', authStore.accessToken ? '已设置' : '未设置');
  console.log('  - userInfo:', authStore.userInfo ? '已设置' : '未设置');
  console.log('  - userMenus 数量:', authStore.userMenus.length);
  
  // 2. 检查 localStorage
  const localStorageData = localStorage.getItem('auth-store');
  console.log('💾 localStorage 数据:');
  if (localStorageData) {
    try {
      const parsedData = JSON.parse(localStorageData);
      console.log('  - 数据已保存:', Object.keys(parsedData));
      console.log('  - 完整数据:', parsedData);
    } catch (error) {
      console.error('  - 数据解析失败:', error);
    }
  } else {
    console.log('  - 无数据');
  }
  
  // 3. 设置测试数据
  console.log('🔧 设置测试数据...');
  authStore.loginStatus = LoginStatus.LOGGED_IN;
  authStore.accessToken = 'test-token-' + Date.now();
  authStore.userInfo = {
    id: 'test-user-' + Date.now(),
    username: 'testuser',
    nickname: '测试用户',
    avatar: '',
    email: '<EMAIL>',
    phone: '13800138000',
    status: 1,
    roles: [{ key: 'user', name: '普通用户' }]
  };
  
  // 4. 等待一下让插件保存数据
  setTimeout(() => {
    const updatedData = localStorage.getItem('auth-store');
    console.log('✅ 数据保存后:');
    if (updatedData) {
      try {
        const parsedData = JSON.parse(updatedData);
        console.log('  - 保存成功:', Object.keys(parsedData));
        console.log('  - loginStatus:', parsedData.loginStatus);
        console.log('  - accessToken:', parsedData.accessToken ? '已保存' : '未保存');
        console.log('  - userInfo:', parsedData.userInfo ? '已保存' : '未保存');
      } catch (error) {
        console.error('  - 数据解析失败:', error);
      }
    } else {
      console.log('  - ❌ 数据未保存到 localStorage');
    }
    
    console.log('🎯 测试完成！请刷新页面验证数据是否恢复。');
  }, 100);
}

/**
 * 清除测试数据
 */
export function clearTestData() {
  console.log('🧹 清除测试数据...');
  
  const authStore = useAuthStore();
  authStore.clearAuthData();
  
  setTimeout(() => {
    const data = localStorage.getItem('auth-store');
    console.log('🗑️ 清除后状态:');
    console.log('  - localStorage:', data ? '仍有数据' : '已清空');
    console.log('  - loginStatus:', authStore.loginStatus);
    console.log('  - accessToken:', authStore.accessToken ? '仍存在' : '已清空');
  }, 100);
}

/**
 * 在浏览器控制台中暴露测试函数
 */
if (typeof window !== 'undefined') {
  (window as any).testPersistence = testPersistence;
  (window as any).clearTestData = clearTestData;
  console.log('🔧 持久化测试工具已加载！');
  console.log('  - 运行 testPersistence() 测试持久化');
  console.log('  - 运行 clearTestData() 清除数据');
}
