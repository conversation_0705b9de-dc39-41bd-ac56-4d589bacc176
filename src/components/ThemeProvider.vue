<template>
  <a-config-provider :theme="antdThemeConfig">
    <div class="theme-provider-root" :style="globalCssVariables">
      <slot></slot>
    </div>
  </a-config-provider>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed, watch } from "vue";
import { theme as antTheme } from "ant-design-vue";
import { subscribeTheme, themeState } from "@/theme/index";

// 响应式主题配置
const themeConfig = ref(themeState.theme);

// 将自定义主题配置转换为Ant Design所需的ThemeConfig格式
const antdThemeConfig = computed(() => {
  const currentTheme = themeConfig.value;
  return {
    // { colorPrimary: string }
    token: currentTheme.token,
    // 执行函数获取算法
    algorithm: currentTheme.algorithm(),
  };
});

// 获取主题token - 重要：必须在antdThemeConfig更新后获取
const { token } = antTheme.useToken();

// 全局CSS变量
const globalCssVariables = computed(() => {
  const tokenValue = token.value;
  return {
    // 背景色相关
    "--theme-bg-base": tokenValue.colorBgBase,
    "--theme-bg-container": tokenValue.colorBgContainer,
    "--theme-bg-layout": tokenValue.colorBgLayout,
    "--theme-bg-spotlight": tokenValue.colorBgSpotlight,
    "--theme-bg-elevated": tokenValue.colorBgElevated,

    // 文字色相关
    "--theme-text-base": tokenValue.colorTextBase,
    "--theme-text": tokenValue.colorText,
    "--theme-text-secondary": tokenValue.colorTextSecondary,
    "--theme-text-tertiary": tokenValue.colorTextTertiary,
    "--theme-text-quaternary": tokenValue.colorTextQuaternary,

    // 主题色相关
    "--theme-primary": tokenValue.colorPrimary,
    "--theme-primary-bg": tokenValue.colorPrimaryBg,
    "--theme-primary-border": tokenValue.colorPrimaryBorder,
    "--theme-primary-hover": tokenValue.colorPrimaryHover,
    "--theme-primary-active": tokenValue.colorPrimaryActive,

    // 边框色相关
    "--theme-border": tokenValue.colorBorder,
    "--theme-border-secondary": tokenValue.colorBorderSecondary,

    // 成功色相关
    "--theme-success": tokenValue.colorSuccess,
    "--theme-success-bg": tokenValue.colorSuccessBg,
    "--theme-success-border": tokenValue.colorSuccessBorder,

    // 警告色相关
    "--theme-warning": tokenValue.colorWarning,
    "--theme-warning-bg": tokenValue.colorWarningBg,
    "--theme-warning-border": tokenValue.colorWarningBorder,

    // 错误色相关
    "--theme-error": tokenValue.colorError,
    "--theme-error-bg": tokenValue.colorErrorBg,
    "--theme-error-border": tokenValue.colorErrorBorder,

    // 信息色相关
    "--theme-info": tokenValue.colorInfo,
    "--theme-info-bg": tokenValue.colorInfoBg,
    "--theme-info-border": tokenValue.colorInfoBorder,

    // 其他常用色
    "--theme-fill": tokenValue.colorFill,
    "--theme-fill-secondary": tokenValue.colorFillSecondary,
    "--theme-fill-tertiary": tokenValue.colorFillTertiary,
    "--theme-fill-quaternary": tokenValue.colorFillQuaternary,
  };
});

onMounted(() => {
  const unsubscribe = subscribeTheme((newTheme) => {
    console.log("ThemeProvider 收到新的主题变化:", newTheme);
    themeConfig.value = newTheme;
  });
  onUnmounted(unsubscribe); // 组件卸载时取消订阅
});

// 监听Ant Design主题配置变化
watch(
  antdThemeConfig,
  (newConfig, oldConfig) => {
    // console.log("Ant Design主题配置已更新:", {
    //   old: oldConfig,
    //   new: newConfig,
    // });
  },
  { deep: true }
);

// 监听CSS变量变化
watch(
  globalCssVariables,
  (newVariables) => {
    // console.log("CSS Variables updated:", newVariables);
  },
  { deep: true }
);
</script>

<style scoped>
.theme-provider-root {
  width: 100%;
  height: 100%;
}
</style>
