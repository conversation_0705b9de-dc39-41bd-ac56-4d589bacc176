<!--
  菜单演示组件
  展示动态菜单渲染功能
-->
<template>
  <div class="menu-demo">
    <a-card title="动态菜单演示" class="demo-card">
      <a-row :gutter="24">
        <!-- 左侧：菜单预览 -->
        <a-col :span="12">
          <a-card size="small" title="菜单预览" class="preview-card">
            <a-menu
              v-model:selectedKeys="selectedKeys"
              v-model:openKeys="openKeys"
              mode="inline"
              :items="menuItems"
              @click="handleMenuClick"
              @openChange="handleOpenChange"
              style="border: 1px solid #d9d9d9; border-radius: 6px;"
            />
          </a-card>
        </a-col>

        <!-- 右侧：控制面板 -->
        <a-col :span="12">
          <a-space direction="vertical" size="middle" style="width: 100%">
            <!-- 操作按钮 -->
            <a-card size="small" title="操作控制">
              <a-space wrap>
                <a-button type="primary" @click="loadMockData">
                  加载测试菜单
                </a-button>
                <a-button @click="clearMenuData">
                  清空菜单
                </a-button>
                <a-button @click="refreshMenu" :loading="menuLoading">
                  刷新菜单
                </a-button>
                <a-button @click="resetMenuState">
                  重置状态
                </a-button>
              </a-space>
            </a-card>

            <!-- 菜单状态 -->
            <a-card size="small" title="菜单状态">
              <a-descriptions size="small" :column="1" bordered>
                <a-descriptions-item label="菜单总数">
                  {{ allMenuKeys.length }}
                </a-descriptions-item>
                <a-descriptions-item label="当前选中">
                  {{ selectedKeys.join(', ') || '无' }}
                </a-descriptions-item>
                <a-descriptions-item label="当前展开">
                  {{ openKeys.join(', ') || '无' }}
                </a-descriptions-item>
                <a-descriptions-item label="当前路由菜单">
                  {{ currentMenuKey || '无' }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>

            <!-- 面包屑 -->
            <a-card size="small" title="面包屑导航">
              <a-breadcrumb v-if="breadcrumb.length > 0">
                <a-breadcrumb-item v-for="item in breadcrumb" :key="item.title">
                  {{ item.title }}
                </a-breadcrumb-item>
              </a-breadcrumb>
              <a-empty v-else description="暂无面包屑" :image="false" />
            </a-card>
          </a-space>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useMenu } from '@/composables/useMenu';
import { useAuthStore } from '@/stores/modules/auth';
import { getMockMenuData } from '@/mock/menu';
import { message } from 'ant-design-vue';

// 菜单管理
const {
  selectedKeys,
  openKeys,
  menuItems,
  allMenuKeys,
  currentMenuKey,
  menuLoading,
  handleMenuClick,
  handleOpenChange,
  refreshMenu,
  resetMenuState,
  getBreadcrumb,
} = useMenu();

// 认证状态
const authStore = useAuthStore();

// 面包屑数据
const breadcrumb = computed(() => getBreadcrumb());

/**
 * 加载模拟数据
 */
const loadMockData = () => {
  try {
    const mockData = getMockMenuData();
    authStore.userMenus = mockData;
    message.success(`已加载 ${mockData.length} 个菜单项`);
  } catch (error) {
    console.error('加载模拟数据失败:', error);
    message.error('加载模拟数据失败');
  }
};

/**
 * 清空菜单数据
 */
const clearMenuData = () => {
  try {
    authStore.userMenus = [];
    resetMenuState();
    message.success('菜单数据已清空');
  } catch (error) {
    console.error('清空数据失败:', error);
    message.error('清空数据失败');
  }
};
</script>

<style scoped>
.menu-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.preview-card {
  height: 100%;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: #262626;
  width: 100px;
}

:deep(.ant-descriptions-item-content) {
  color: #595959;
}

:deep(.ant-menu) {
  max-height: 400px;
  overflow-y: auto;
}
</style>
