/**
 * 菜单管理组合函数
 * 用于处理菜单状态和操作
 */

import { ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/modules/auth';
import { 
  transformMenusToAntd, 
  findMenuKeyByPath, 
  getMenuParentKeys,
  extractMenuKeys
} from '@/utils/menu';
import type { MenuRecord } from '@/types/auth';
import type { AntdMenuItemType, MenuTransformConfig } from '@/types/menu';

/**
 * 菜单管理组合函数
 * @param config 菜单转换配置
 */
export const useMenu = (config: Partial<MenuTransformConfig> = {}) => {
  const route = useRoute();
  const router = useRouter();
  const authStore = useAuthStore();

  // ===== 响应式状态 =====
  
  /** 当前选中的菜单键 */
  const selectedKeys = ref<string[]>([]);
  
  /** 当前展开的菜单键 */
  const openKeys = ref<string[]>([]);
  
  /** 菜单是否正在加载 */
  const menuLoading = ref(false);

  // ===== 计算属性 =====
  
  /** 原始菜单数据 */
  const rawMenus = computed<MenuRecord[]>(() => authStore.userMenus);
  
  /** 转换后的 Ant Design 菜单数据 */
  const menuItems = computed<AntdMenuItemType[]>(() => {
    if (!rawMenus.value || rawMenus.value.length === 0) {
      return [];
    }
    return transformMenusToAntd(rawMenus.value, config);
  });
  
  /** 所有可用的菜单键 */
  const allMenuKeys = computed<string[]>(() => {
    if (!rawMenus.value || rawMenus.value.length === 0) {
      return [];
    }
    return extractMenuKeys(rawMenus.value);
  });
  
  /** 当前路由对应的菜单键 */
  const currentMenuKey = computed<string | null>(() => {
    if (!rawMenus.value || rawMenus.value.length === 0) {
      return null;
    }
    return findMenuKeyByPath(rawMenus.value, route.path);
  });
  
  /** 当前菜单的父级键 */
  const currentParentKeys = computed<string[]>(() => {
    if (!rawMenus.value || !currentMenuKey.value) {
      return [];
    }
    return getMenuParentKeys(rawMenus.value, currentMenuKey.value);
  });

  // ===== 方法定义 =====
  
  /**
   * 设置选中的菜单
   * @param keys 菜单键数组
   */
  const setSelectedKeys = (keys: string[]) => {
    selectedKeys.value = keys;
  };
  
  /**
   * 设置展开的菜单
   * @param keys 菜单键数组
   */
  const setOpenKeys = (keys: string[]) => {
    openKeys.value = keys;
  };
  
  /**
   * 根据当前路由更新菜单状态
   */
  const updateMenuByRoute = () => {
    if (currentMenuKey.value) {
      // 设置选中的菜单
      setSelectedKeys([currentMenuKey.value]);
      
      // 设置展开的父级菜单
      if (currentParentKeys.value.length > 0) {
        setOpenKeys(currentParentKeys.value);
      }
    }
  };
  
  /**
   * 处理菜单点击事件
   * @param menuInfo 菜单信息
   */
  const handleMenuClick = ({ key }: { key: string }) => {
    // 查找对应的菜单项
    const findMenuByKey = (menus: MenuRecord[], targetKey: string): MenuRecord | null => {
      for (const menu of menus) {
        if (menu.id.toString() === targetKey) {
          return menu;
        }
        if (menu.children && menu.children.length > 0) {
          const found = findMenuByKey(menu.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };
    
    const menuItem = findMenuByKey(rawMenus.value, key);
    if (menuItem && menuItem.path) {
      // 如果菜单有路径，则进行路由跳转
      if (menuItem.path !== route.path) {
        router.push(menuItem.path);
      }
    }
  };
  
  /**
   * 处理子菜单展开/收起事件
   * @param keys 展开的菜单键数组
   */
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };
  
  /**
   * 刷新菜单数据
   */
  const refreshMenu = async () => {
    menuLoading.value = true;
    try {
      // 重新获取用户菜单
      await authStore.getUserMenus();
      // 更新菜单状态
      updateMenuByRoute();
    } catch (error) {
      console.error('刷新菜单失败:', error);
    } finally {
      menuLoading.value = false;
    }
  };
  
  /**
   * 重置菜单状态
   */
  const resetMenuState = () => {
    selectedKeys.value = [];
    openKeys.value = [];
  };
  
  /**
   * 检查菜单项是否存在
   * @param key 菜单键
   * @returns 是否存在
   */
  const hasMenuItem = (key: string): boolean => {
    return allMenuKeys.value.includes(key);
  };
  
  /**
   * 获取菜单项信息
   * @param key 菜单键
   * @returns 菜单项信息
   */
  const getMenuItem = (key: string): MenuRecord | null => {
    const findMenuByKey = (menus: MenuRecord[], targetKey: string): MenuRecord | null => {
      for (const menu of menus) {
        if (menu.id.toString() === targetKey) {
          return menu;
        }
        if (menu.children && menu.children.length > 0) {
          const found = findMenuByKey(menu.children, targetKey);
          if (found) return found;
        }
      }
      return null;
    };
    
    return findMenuByKey(rawMenus.value, key);
  };
  
  /**
   * 获取面包屑数据
   * @param key 菜单键
   * @returns 面包屑数组
   */
  const getBreadcrumb = (key?: string): Array<{ title: string; path?: string }> => {
    const targetKey = key || currentMenuKey.value;
    if (!targetKey) return [];
    
    const breadcrumb: Array<{ title: string; path?: string }> = [];
    const parentKeys = getMenuParentKeys(rawMenus.value, targetKey);
    const allKeys = [...parentKeys, targetKey];
    
    allKeys.forEach(menuKey => {
      const menuItem = getMenuItem(menuKey);
      if (menuItem) {
        breadcrumb.push({
          title: menuItem.title || menuItem.name,
          path: menuItem.path || undefined,
        });
      }
    });
    
    return breadcrumb;
  };

  // ===== 监听器 =====
  
  // 监听路由变化，自动更新菜单状态
  watch(
    () => route.path,
    () => {
      updateMenuByRoute();
    },
    { immediate: true }
  );
  
  // 监听菜单数据变化，自动更新菜单状态
  watch(
    () => rawMenus.value,
    () => {
      updateMenuByRoute();
    },
    { immediate: true }
  );

  // ===== 返回值 =====
  
  return {
    // 状态
    selectedKeys,
    openKeys,
    menuLoading,
    
    // 计算属性
    rawMenus,
    menuItems,
    allMenuKeys,
    currentMenuKey,
    currentParentKeys,
    
    // 方法
    setSelectedKeys,
    setOpenKeys,
    updateMenuByRoute,
    handleMenuClick,
    handleOpenChange,
    refreshMenu,
    resetMenuState,
    hasMenuItem,
    getMenuItem,
    getBreadcrumb,
  };
};
