/**
 * API接口地址常量定义
 * 统一管理所有接口路径，便于维护和修改
 */

/**
 * 认证相关接口
 */
export const AUTH_API = {
  /** 用户登录 */
  LOGIN: '/auth/login',
  /** 用户登出 */
  LOGOUT: '/auth/logout',
  /** 刷新token */
  REFRESH_TOKEN: '/auth/refresh',
  /** 获取当前用户信息 */
  CURRENT_USER: '/auth/user',
  /** 获取用户菜单权限 */
  USER_MENUS: '/auth/menus',
  /** 修改密码 */
  CHANGE_PASSWORD: '/auth/change-password',
  /** 验证token有效性 */
  VALIDATE_TOKEN: '/auth/validate',
} as const;

/**
 * 用户管理接口
 */
export const USER_API = {
  /** 获取用户列表 */
  LIST: '/users',
  /** 分页查询用户 */
  PAGE: '/users/page',
  /** 根据ID获取用户详情 */
  DETAIL: (id: number) => `/users/${id}`,
  /** 新增用户 */
  CREATE: '/users',
  /** 修改用户 */
  UPDATE: (id: number) => `/users/${id}`,
  /** 删除用户 */
  DELETE: (id: number) => `/users/${id}`,
  /** 批量删除用户 */
  BATCH_DELETE: '/users/batch-delete',
  /** 重置用户密码 */
  RESET_PASSWORD: (id: number) => `/users/${id}/reset-password`,
  /** 启用/禁用用户 */
  TOGGLE_STATUS: (id: number) => `/users/${id}/status`,
} as const;

/**
 * 客户管理接口
 */
export const CUSTOMER_API = {
  /** 分页查询客户 */
  PAGE: '/customers/page',
  /** 根据ID获取客户详情 */
  DETAIL: (id: number) => `/customers/${id}`,
  /** 新增客户 */
  CREATE: '/customers',
  /** 更新客户 */
  UPDATE: (id: number) => `/customers/${id}`,
  /** 删除客户 */
  DELETE: (id: number) => `/customers/${id}`,
  /** 批量删除客户 */
  BATCH_DELETE: '/customers/batch-delete',
  /** 导出客户数据 */
  EXPORT: '/customers/export',
  /** 导入客户数据 */
  IMPORT: '/customers/import',
  /** 获取客户统计信息 */
  STATS: '/customers/stats',
} as const;

/**
 * 角色管理接口
 */
export const ROLE_API = {
  /** 获取角色列表 */
  LIST: '/roles',
  /** 分页查询角色 */
  PAGE: '/roles/page',
  /** 根据ID获取角色详情 */
  DETAIL: (id: number) => `/roles/${id}`,
  /** 新增角色 */
  CREATE: '/roles',
  /** 修改角色 */
  UPDATE: (id: number) => `/roles/${id}`,
  /** 删除角色 */
  DELETE: (id: number) => `/roles/${id}`,
  /** 获取角色权限菜单 */
  MENUS: (id: number) => `/roles/${id}/menus`,
  /** 设置角色权限菜单 */
  SET_MENUS: (id: number) => `/roles/${id}/menus`,
  /** 复制角色 */
  COPY: (id: number) => `/roles/${id}/copy`,
  /** 启用/禁用角色 */
  TOGGLE_STATUS: (id: number) => `/roles/${id}/status`,
} as const;

/**
 * 菜单管理接口
 */
export const MENU_API = {
  /** 获取菜单列表 */
  LIST: '/menus',
  /** 获取树形菜单列表 */
  TREE: '/menus/tree',
  /** 分页查询菜单 */
  PAGE: '/menus/page',
  /** 根据ID获取菜单详情 */
  DETAIL: (id: number) => `/menus/${id}`,
  /** 新增菜单 */
  CREATE: '/menus',
  /** 修改菜单 */
  UPDATE: (id: number) => `/menus/${id}`,
  /** 删除菜单 */
  DELETE: (id: number) => `/menus/${id}`,
  /** 获取父级菜单选项 */
  PARENT_OPTIONS: '/menus/parent-options',
  /** 移动菜单位置 */
  MOVE: (id: number) => `/menus/${id}/move`,
  /** 批量更新菜单排序 */
  BATCH_SORT: '/menus/batch-sort',
  /** 启用/禁用菜单 */
  TOGGLE_STATUS: (id: number) => `/menus/${id}/status`,
} as const;

/**
 * 通用接口
 */
export const COMMON_API = {
  /** 文件上传 */
  UPLOAD: '/upload',
  /** 文件下载 */
  DOWNLOAD: (fileId: string) => `/download/${fileId}`,
  /** 获取验证码 */
  CAPTCHA: '/captcha',
  /** 发送短信验证码 */
  SMS_CODE: '/sms/code',
  /** 获取系统配置 */
  SYSTEM_CONFIG: '/system/config',
  /** 获取字典数据 */
  DICT_DATA: (dictType: string) => `/dict/data/${dictType}`,
} as const;

/**
 * API版本管理
 */
export const API_VERSION = {
  V1: '/v1',
  V2: '/v2',
} as const;

/**
 * API基础路径
 */
export const API_BASE = {
  /** 认证模块 */
  AUTH: '/auth',
  /** 用户模块 */
  USER: '/users',
  /** 客户模块 */
  CUSTOMER: '/customers',
  /** 角色模块 */
  ROLE: '/roles',
  /** 菜单模块 */
  MENU: '/menus',
  /** 系统模块 */
  SYSTEM: '/system',
  /** 文件模块 */
  FILE: '/file',
} as const;

/**
 * 导出所有API常量
 */
export const API_ENDPOINTS = {
  AUTH: AUTH_API,
  USER: USER_API,
  CUSTOMER: CUSTOMER_API,
  ROLE: ROLE_API,
  MENU: MENU_API,
  COMMON: COMMON_API,
} as const;

/**
 * API路径构建工具函数
 */
export class ApiPathBuilder {
  /**
   * 构建带版本的API路径
   * @param version API版本
   * @param path API路径
   * @returns 完整的API路径
   */
  static withVersion(version: string, path: string): string {
    return `${version}${path}`;
  }
  
  /**
   * 构建带基础路径的API路径
   * @param base 基础路径
   * @param path API路径
   * @returns 完整的API路径
   */
  static withBase(base: string, path: string): string {
    return `${base}${path}`;
  }
  
  /**
   * 构建查询参数
   * @param params 参数对象
   * @returns 查询字符串
   */
  static buildQuery(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString();
  }
}
