/**
 * 本地存储常量定义
 * 统一管理所有localStorage和sessionStorage的key，避免硬编码
 */

// ===== 认证相关存储Key =====
export const AUTH_STORAGE_KEYS = {
  /** 访问令牌 */
  ACCESS_TOKEN: 'combo_opm_access_token',
  /** 刷新令牌 */
  REFRESH_TOKEN: 'combo_opm_refresh_token',
  /** 用户信息 */
  USER_INFO: 'combo_opm_user_info',
  /** 用户菜单权限 */
  USER_MENUS: 'combo_opm_user_menus',
  /** 用户权限列表 */
  USER_PERMISSIONS: 'combo_opm_user_permissions',
  /** 用户角色列表 */
  USER_ROLES: 'combo_opm_user_roles',
  /** 登录状态 */
  LOGIN_STATUS: 'combo_opm_login_status',
  /** Token过期时间 */
  TOKEN_EXPIRES_AT: 'combo_opm_token_expires_at',
} as const;

// ===== Pinia 持久化存储Key =====
export const PINIA_PERSIST_KEYS = {
  /** 认证状态存储 */
  AUTH_STORE: 'combo_opm_auth_store',
} as const;

// ===== 用户偏好设置存储Key =====
export const USER_PREFERENCE_KEYS = {
  /** 主题设置 */
  THEME: 'combo_opm_theme',
  /** 语言设置 */
  LANGUAGE: 'combo_opm_language',
  /** 用户偏好设置 */
  USER_PREFERENCES: 'combo_opm_user_preferences',
  /** 侧边栏折叠状态 */
  SIDEBAR_COLLAPSED: 'combo_opm_sidebar_collapsed',
  /** 表格列设置 */
  TABLE_COLUMNS: 'combo_opm_table_columns',
} as const;

// ===== 应用设置存储Key =====
export const APP_STORAGE_KEYS = {
  /** 应用版本 */
  APP_VERSION: 'combo_opm_app_version',
  /** 最后访问时间 */
  LAST_VISIT_TIME: 'combo_opm_last_visit_time',
  /** 路由缓存 */
  ROUTE_CACHE: 'combo_opm_route_cache',
  /** 表单草稿 */
  FORM_DRAFT: 'combo_opm_form_draft',
} as const;

// ===== 业务数据缓存Key =====
export const CACHE_STORAGE_KEYS = {
  /** 客户列表缓存 */
  CUSTOMER_LIST: 'combo_opm_cache_customer_list',
  /** 用户列表缓存 */
  USER_LIST: 'combo_opm_cache_user_list',
  /** 角色列表缓存 */
  ROLE_LIST: 'combo_opm_cache_role_list',
  /** 菜单列表缓存 */
  MENU_LIST: 'combo_opm_cache_menu_list',
  /** 字典数据缓存 */
  DICT_DATA: 'combo_opm_cache_dict_data',
} as const;

// ===== 存储类型定义 =====
export type AuthStorageKey = typeof AUTH_STORAGE_KEYS[keyof typeof AUTH_STORAGE_KEYS];
export type UserPreferenceKey = typeof USER_PREFERENCE_KEYS[keyof typeof USER_PREFERENCE_KEYS];
export type AppStorageKey = typeof APP_STORAGE_KEYS[keyof typeof APP_STORAGE_KEYS];
export type CacheStorageKey = typeof CACHE_STORAGE_KEYS[keyof typeof CACHE_STORAGE_KEYS];

// ===== 导出所有存储Key =====
export const STORAGE_KEYS = {
  AUTH: AUTH_STORAGE_KEYS,
  USER_PREFERENCE: USER_PREFERENCE_KEYS,
  APP: APP_STORAGE_KEYS,
  CACHE: CACHE_STORAGE_KEYS,
} as const;

// ===== 存储过期时间常量 =====
export const STORAGE_EXPIRES = {
  /** Token默认过期时间（7天） */
  TOKEN_DEFAULT: 7 * 24 * 60 * 60 * 1000,
  /** 缓存数据过期时间（1小时） */
  CACHE_DEFAULT: 60 * 60 * 1000,
  /** 用户偏好永不过期 */
  USER_PREFERENCE: -1,
} as const;
