import { createRouter, createWebHistory, type Router } from 'vue-router'
import type { App } from 'vue'
import type { MenuItem, RouteGuardContext } from '@/types/router'
import { 
  staticRoutes, 
  mainLayoutRoute,
  generateMainLayoutWithDynamicRoutes,
  filterMenuItemsByPermissions 
} from './router'
import { ROUTE_PATHS } from '@/constants/routes'

/**
 * 创建路由实例
 */
function createAppRouter(): Router {
  const router = createRouter({
    history: createWebHistory(),
    routes: [
      ...staticRoutes,
      mainLayoutRoute,
    ],
    scrollBehavior(to, from, savedPosition) {
      if (savedPosition) {
        return savedPosition
      } else {
        return { top: 0 }
      }
    },
  })

  return router
}

// 创建路由实例
const router = createAppRouter()

/**
 * 动态添加路由
 * @param menuItems 菜单项列表
 */
export function addDynamicRoutes(menuItems: MenuItem[]): void {
  // 移除现有的主布局路由
  if (router.hasRoute('Layout')) {
    router.removeRoute('Layout')
  }

  // 生成包含动态路由的主布局路由
  const mainLayoutWithDynamicRoutes = generateMainLayoutWithDynamicRoutes(menuItems)
  
  // 添加新的主布局路由
  router.addRoute(mainLayoutWithDynamicRoutes)
}

/**
 * 根据用户权限添加动态路由
 * @param menuItems 菜单项列表
 * @param permissions 用户权限列表
 * @param roles 用户角色列表
 */
export function addDynamicRoutesByPermissions(
  menuItems: MenuItem[],
  permissions: string[] = [],
  roles: string[] = []
): void {
  // 根据权限过滤菜单项
  const filteredMenuItems = filterMenuItemsByPermissions(menuItems, permissions, roles)
  
  // 添加过滤后的动态路由
  addDynamicRoutes(filteredMenuItems)
}

/**
 * 清除动态路由
 */
export function clearDynamicRoutes(): void {
  if (router.hasRoute('Layout')) {
    router.removeRoute('Layout')
  }
  
  // 重新添加基础的主布局路由
  router.addRoute(mainLayoutRoute)
}

/**
 * 路由前置守卫
 */
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Opm`
  }

  // 动态导入store以避免循环依赖
  const { useAuthStore } = await import('@/stores')
  const authStore = useAuthStore()

  // 等待认证状态初始化完成
  if (!authStore.isInitialized) {
    // 如果认证状态还未初始化，等待初始化完成
    await authStore.initAuth()
  }

  // 检查是否需要认证
  const requireAuth = to.meta?.requireAuth !== false
  const isAuthenticated = authStore.isLoggedIn

  if (requireAuth && !isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    if (to.path !== ROUTE_PATHS.LOGIN) {
      next({
        path: ROUTE_PATHS.LOGIN,
        query: { redirect: to.fullPath },
      })
      return
    }
  }

  // 检查管理员权限
  if (to.meta?.requireAdmin && !authStore.hasRole('admin')) {
    next({ path: ROUTE_PATHS.FORBIDDEN })
    return
  }

  // 如果已登录且访问登录页，重定向到首页
  if (isAuthenticated && to.path === ROUTE_PATHS.LOGIN) {
    next({ path: ROUTE_PATHS.DASHBOARD })
    return
  }

  next()
})

/**
 * 路由后置守卫
 */
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计、埋点等逻辑
  console.log(`路由跳转: ${from.path} -> ${to.path}`)
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  console.error('路由错误:', error)
  // 可以在这里添加错误上报逻辑
})

/**
 * 获取用户权限上下文
 * 使用Pinia状态管理获取用户信息
 */
export async function getUserContext(): Promise<RouteGuardContext> {
  const { useAuthStore } = await import('@/stores')
  const authStore = useAuthStore()

  return {
    isAuthenticated: authStore.isLoggedIn,
    roles: authStore.userRoles,
    permissions: authStore.userPermissions,
    user: authStore.userInfo,
  }
}

/**
 * 安装路由到Vue应用
 * @param app Vue应用实例
 */
export function setupRouter(app: App): void {
  app.use(router)
}

/**
 * 获取路由实例
 */
export function getRouter(): Router {
  return router
}

// 导出路由实例
export default router
