import type { RouteRecordRaw } from 'vue-router'
import type { MenuItem, ExtendedRouteRecordRaw, GenerateRoutesParams } from '@/types/router'
import { ROUTE_PATHS, ROUTE_NAMES, PAGE_TITLES } from '@/constants/routes'

/**
 * 固定路由配置
 * 这些路由不依赖权限，始终可访问
 */
export const staticRoutes: RouteRecordRaw[] = [
  {
    path: ROUTE_PATHS.ROOT,
    redirect: ROUTE_PATHS.LOGIN,
  },
  {
    path: ROUTE_PATHS.LOGIN,
    name: ROUTE_NAMES.LOGIN,
    component: () => import('@/views/Login.vue'),
    meta: {
      title: PAGE_TITLES.LOGIN,
      hideInMenu: true,
      requireAuth: false,
    },
  },
  {
    path: ROUTE_PATHS.NOT_FOUND,
    name: ROUTE_NAMES.NOT_FOUND,
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: PAGE_TITLES.NOT_FOUND,
      hideInMenu: true,
      requireAuth: false,
    },
  },
  {
    path: ROUTE_PATHS.FORBIDDEN,
    name: ROUTE_NAMES.FORBIDDEN,
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: PAGE_TITLES.FORBIDDEN,
      hideInMenu: true,
      requireAuth: false,
    },
  },
  {
    path: ROUTE_PATHS.SERVER_ERROR,
    name: ROUTE_NAMES.SERVER_ERROR,
    component: () => import('@/views/error/500.vue'),
    meta: {
      title: PAGE_TITLES.SERVER_ERROR,
      hideInMenu: true,
      requireAuth: false,
    },
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: ROUTE_PATHS.NOT_FOUND,
  },
]

/**
 * 主布局路由配置
 * 需要认证的页面都在这个布局下
 */
export const mainLayoutRoute: RouteRecordRaw = {
  path: '/',
  name: 'Layout',
  component: () => import('@/layout/MainLayout.vue'),
  redirect: ROUTE_PATHS.DASHBOARD,
  meta: {
    requireAuth: true,
  },
  children: [
    {
      path: ROUTE_PATHS.DASHBOARD,
      name: ROUTE_NAMES.DASHBOARD,
      component: () => import('@/views/Dashboard.vue'),
      meta: {
        title: PAGE_TITLES.DASHBOARD,
        icon: 'DashboardOutlined',
        requireAuth: true,
        keepAlive: true,
      },
    },
  ],
}

/**
 * 将MenuItem转换为RouteRecordRaw
 * @param menuItem 菜单项
 * @param parentPath 父级路径
 * @param lazy 是否懒加载
 * @returns RouteRecordRaw
 */
function menuItemToRoute(
  menuItem: MenuItem,
  parentPath = '',
  lazy = true
): ExtendedRouteRecordRaw {
  const fullPath = parentPath ? `${parentPath}${menuItem.path}` : menuItem.path

  const route: ExtendedRouteRecordRaw = {
    path: menuItem.path,
    name: menuItem.name,
    meta: {
      title: menuItem.title,
      icon: menuItem.icon,
      hideInMenu: menuItem.hideInMenu || false,
      requireAuth: menuItem.requireAuth !== false, // 默认需要认证
      requireAdmin: menuItem.requireAdmin || false,
      keepAlive: menuItem.keepAlive || false,
      sort: menuItem.sort || 0,
      breadcrumb: menuItem.breadcrumb,
      ...menuItem.meta,
    },
  }

  // 处理组件
  if (menuItem.component) {
    if (typeof menuItem.component === 'string') {
      // 字符串路径，使用懒加载
      if (lazy) {
        route.component = () => import(`@/views/${menuItem.component}.vue`)
      } else {
        // 这里可以根据需要实现同步加载
        route.component = () => import(`@/views/${menuItem.component}.vue`)
      }
    } else {
      // 直接传入的组件对象
      route.component = menuItem.component
    }
  }

  // 处理重定向
  if (menuItem.redirect) {
    route.redirect = menuItem.redirect
  }

  // 处理子路由
  if (menuItem.children && menuItem.children.length > 0) {
    route.children = menuItem.children.map(child =>
      menuItemToRoute(child, fullPath, lazy)
    )
  }

  return route
}

/**
 * 生成动态路由
 * @param params 生成参数
 * @returns RouteRecordRaw[]
 */
export function generateDynamicRoutes(params: GenerateRoutesParams): ExtendedRouteRecordRaw[] {
  const { menuItems, parentPath = '', lazy = true } = params

  return menuItems.map(menuItem => menuItemToRoute(menuItem, parentPath, lazy))
}

/**
 * 将动态路由添加到主布局中
 * @param menuItems 菜单项列表
 * @returns 完整的主布局路由
 */
export function generateMainLayoutWithDynamicRoutes(menuItems: MenuItem[]): RouteRecordRaw {
  const dynamicRoutes = generateDynamicRoutes({ menuItems, lazy: true })
  
  return {
    ...mainLayoutRoute,
    children: [
      ...(mainLayoutRoute.children || []),
      ...dynamicRoutes,
    ],
  }
}

/**
 * 根据权限过滤菜单项
 * @param menuItems 菜单项列表
 * @param permissions 用户权限列表
 * @param roles 用户角色列表
 * @returns 过滤后的菜单项列表
 */
export function filterMenuItemsByPermissions(
  menuItems: MenuItem[],
  permissions: string[] = [],
  roles: string[] = []
): MenuItem[] {
  return menuItems.filter(item => {
    // 检查是否需要管理员权限
    if (item.requireAdmin && !roles.includes('admin')) {
      return false
    }

    // 检查权限（如果有权限要求）
    if (item.meta?.permissions) {
      const hasPermission = item.meta.permissions.some((permission: string) =>
        permissions.includes(permission)
      )
      if (!hasPermission) {
        return false
      }
    }

    // 检查角色（如果有角色要求）
    if (item.meta?.roles) {
      const hasRole = item.meta.roles.some((role: string) => roles.includes(role))
      if (!hasRole) {
        return false
      }
    }

    // 递归过滤子菜单
    if (item.children) {
      item.children = filterMenuItemsByPermissions(item.children, permissions, roles)
    }

    return true
  })
}

/**
 * 获取所有路由路径
 * @param routes 路由列表
 * @param basePath 基础路径
 * @returns 路径列表
 */
export function getAllRoutePaths(routes: RouteRecordRaw[], basePath = ''): string[] {
  const paths: string[] = []

  routes.forEach(route => {
    const fullPath = basePath + route.path
    paths.push(fullPath)

    if (route.children) {
      paths.push(...getAllRoutePaths(route.children, fullPath))
    }
  })

  return paths
}
