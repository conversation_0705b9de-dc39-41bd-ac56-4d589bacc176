/**
 * 用户相关状态管理 Store
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { AuthApi } from '@/api/auth';
import { StorageManager } from '@/utils/storage';
import { USER_PREFERENCE_KEYS } from '@/constants/storage';
import type { UserLoginRecord } from '@/types';

export const useUserStore = defineStore('user', () => {
  // ===== 状态定义 =====
  const userProfile = ref<UserLoginRecord | null>(null);
  const userPreferences = ref({
    theme: 'light',
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: '24h'
  });

  // ===== 计算属性 =====
  const userDisplayInfo = computed(() => {
    if (!userProfile.value) return null;
    
    return {
      id: userProfile.value.id,
      username: userProfile.value.username,
      nickname: userProfile.value.nickname,
      avatar: userProfile.value.avatar,
      email: userProfile.value.email,
      phone: userProfile.value.phone,
      displayName: userProfile.value.nickname || userProfile.value.username,
      isActive: userProfile.value.status === 1
    };
  });

  const userStats = computed(() => {
    if (!userProfile.value) return null;
    
    return {
      roleCount: userProfile.value.roles?.length || 0,
      lastLoginTime: new Date().toISOString(), // 这里应该从API获取
      accountAge: '新用户' // 这里应该计算账户年龄
    };
  });

  // ===== Actions =====
  
  /**
   * 设置用户资料
   * @param profile 用户资料
   */
  const setUserProfile = (profile: UserLoginRecord): void => {
    userProfile.value = profile;
  };

  /**
   * 更新用户资料
   * @param updates 要更新的字段
   */
  const updateUserProfile = async (updates: Partial<UserLoginRecord>): Promise<boolean> => {
    try {
      if (!userProfile.value) {
        message.error('用户信息不存在');
        return false;
      }

      // 这里应该调用更新用户信息的API
      // const updatedUser = await UserApi.updateProfile(userProfile.value.id, updates);
      
      // 临时更新本地状态
      userProfile.value = { ...userProfile.value, ...updates };
      
      message.success('用户信息更新成功');
      return true;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      message.error('更新用户信息失败');
      return false;
    }
  };

  /**
   * 修改密码
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   */
  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      await AuthApi.changePassword(oldPassword, newPassword);
      message.success('密码修改成功');
      return true;
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('修改密码失败');
      return false;
    }
  };

  /**
   * 更新用户偏好设置
   * @param preferences 偏好设置
   */
  const updateUserPreferences = (preferences: Partial<typeof userPreferences.value>): void => {
    userPreferences.value = { ...userPreferences.value, ...preferences };

    // 使用StorageManager保存到本地存储
    StorageManager.setItem(USER_PREFERENCE_KEYS.USER_PREFERENCES, userPreferences.value, -1);

    message.success('偏好设置已保存');
  };

  /**
   * 从本地存储加载用户偏好
   */
  const loadUserPreferences = (): void => {
    try {
      const saved = StorageManager.getItem(USER_PREFERENCE_KEYS.USER_PREFERENCES, userPreferences.value);
      if (saved) {
        userPreferences.value = { ...userPreferences.value, ...saved };
      }
    } catch (error) {
      console.warn('加载用户偏好失败:', error);
    }
  };

  /**
   * 清除用户数据
   */
  const clearUserData = (): void => {
    userProfile.value = null;
    StorageManager.removeItem(USER_PREFERENCE_KEYS.USER_PREFERENCES);
  };

  /**
   * 获取用户头像URL
   * @param size 头像尺寸
   */
  const getUserAvatarUrl = (size: number = 40): string => {
    if (userProfile.value?.avatar) {
      return userProfile.value.avatar;
    }
    
    // 返回默认头像或生成头像
    const username = userProfile.value?.username || 'User';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&size=${size}&background=667eea&color=fff`;
  };

  /**
   * 检查用户是否有特定状态
   * @param status 状态值
   */
  const hasStatus = (status: number): boolean => {
    return userProfile.value?.status === status;
  };

  /**
   * 获取用户角色名称列表
   */
  const getUserRoleNames = (): string[] => {
    if (!userProfile.value?.roles) return [];
    return userProfile.value.roles.map(role => role.name);
  };

  /**
   * 检查用户是否有特定角色
   * @param roleName 角色名称
   */
  const hasRole = (roleName: string): boolean => {
    if (!userProfile.value?.roles) return false;
    return userProfile.value.roles.some(role => role.name === roleName || role.key === roleName);
  };

  // 初始化时加载用户偏好
  loadUserPreferences();

  return {
    // 状态
    userProfile,
    userPreferences,
    
    // 计算属性
    userDisplayInfo,
    userStats,
    
    // 方法
    setUserProfile,
    updateUserProfile,
    changePassword,
    updateUserPreferences,
    loadUserPreferences,
    clearUserData,
    getUserAvatarUrl,
    hasStatus,
    getUserRoleNames,
    hasRole
  };
});
