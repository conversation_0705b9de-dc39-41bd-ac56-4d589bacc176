/**
 * 认证状态管理 Store
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { AuthApi } from '@/api/auth';
import { TokenManager } from '@/utils/request';
import type {
  UserLoginReq,
  AuthRecord,
  UserLoginRecord,
  MenuRecord,
  LoginStatusType
} from '@/types';
import { LoginStatus } from '@/types';

export const useAuthStore = defineStore('auth', () => {
  // ===== 状态定义 =====
  const loginStatus = ref<LoginStatusType>(LoginStatus.NOT_LOGGED_IN);
  const userInfo = ref<UserLoginRecord | null>(null);
  const accessToken = ref<string>('');
  const userMenus = ref<MenuRecord[]>([]);
  const userPermissions = ref<string[]>([]);
  const userRoles = ref<string[]>([]);
  const isInitialized = ref<boolean>(false); // 认证状态是否已初始化

  // ===== 计算属性 =====
  const isLoggedIn = computed(() => loginStatus.value === LoginStatus.LOGGED_IN);
  const isLoggingIn = computed(() => loginStatus.value === LoginStatus.LOGGING_IN);
  const isTokenExpired = computed(() => loginStatus.value === LoginStatus.TOKEN_EXPIRED);
  
  // 获取用户昵称或用户名
  const displayName = computed(() => {
    if (!userInfo.value) return '';
    return userInfo.value.nickname || userInfo.value.username || '未知用户';
  });

  // 获取用户头像
  const userAvatar = computed(() => {
    return userInfo.value?.avatar || '';
  });

  // 检查是否有特定权限
  const hasPermission = computed(() => {
    return (permission: string) => {
      return userPermissions.value.includes(permission);
    };
  });

  // 检查是否有特定角色
  const hasRole = computed(() => {
    return (role: string) => {
      return userRoles.value.includes(role);
    };
  });

  // ===== Actions =====
  
  /**
   * 用户登录
   * @param loginData 登录参数
   */
  const login = async (loginData: UserLoginReq): Promise<boolean> => {
    try {
      loginStatus.value = LoginStatus.LOGGING_IN;
      
      // 调用登录API
      const authRecord = await AuthApi.login(loginData);
      
      // 保存认证信息
      await setAuthData(authRecord);
      
      loginStatus.value = LoginStatus.LOGGED_IN;
      message.success('登录成功');
      
      return true;
    } catch (error) {
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      console.error('登录失败:', error);
      message.error('登录失败，请检查用户名和密码');
      return false;
    }
  };

  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    try {
      loginStatus.value = LoginStatus.LOGGING_OUT;
      
      // 调用登出API
      await AuthApi.logout();
    } catch (error) {
      console.warn('登出API调用失败:', error);
    } finally {
      // 无论API是否成功，都要清除本地状态
      clearAuthData();
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      message.success('已退出登录');
    }
  };

  /**
   * 刷新Token
   */
  const refreshToken = async (): Promise<boolean> => {
    try {
      const authRecord = await AuthApi.refreshToken();
      await setAuthData(authRecord);
      loginStatus.value = LoginStatus.LOGGED_IN;
      return true;
    } catch (error) {
      console.error('Token刷新失败:', error);
      loginStatus.value = LoginStatus.TOKEN_EXPIRED;
      clearAuthData();
      return false;
    }
  };

  /**
   * 获取当前用户信息
   */
  const getCurrentUser = async (): Promise<boolean> => {
    try {
      const user = await AuthApi.getCurrentUser();
      userInfo.value = user;
      return true;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return false;
    }
  };

  /**
   * 获取用户菜单权限
   */
  const getUserMenus = async (): Promise<boolean> => {
    try {
      const menus = await AuthApi.getUserMenus();
      userMenus.value = menus;
      return true;
    } catch (error) {
      console.error('获取用户菜单失败:', error);
      return false;
    }
  };

  /**
   * 验证Token有效性
   */
  const validateToken = async (): Promise<boolean> => {
    const token = TokenManager.getToken();
    if (!token) {
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      return false;
    }

    try {
      const isValid = await AuthApi.validateToken();
      if (isValid) {
        loginStatus.value = LoginStatus.LOGGED_IN;
        return true;
      } else {
        loginStatus.value = LoginStatus.TOKEN_EXPIRED;
        clearAuthData();
        return false;
      }
    } catch (error) {
      console.error('Token验证失败:', error);
      loginStatus.value = LoginStatus.TOKEN_EXPIRED;
      clearAuthData();
      return false;
    }
  };

  /**
   * 设置认证数据
   * @param authRecord 认证记录
   */
  const setAuthData = async (authRecord: AuthRecord): Promise<void> => {
    // 保存用户信息到状态
    userInfo.value = authRecord.userInfo;

    // 保存Token到状态和存储
    accessToken.value = authRecord.authInfo.accessToken;
    TokenManager.setToken(authRecord.authInfo.accessToken);

    // 保存菜单权限到状态
    userMenus.value = authRecord.authInfo.menus;

    // 提取角色信息
    const roles = authRecord.userInfo.roles.map(role => role.key);
    userRoles.value = roles;

    // 提取权限信息（从菜单中提取）
    const permissions = extractPermissionsFromMenus(authRecord.authInfo.menus);
    userPermissions.value = permissions;

    // 持久化由 pinia-plugin-persistedstate 自动处理，无需手动保存
  };

  /**
   * 清除认证数据
   */
  const clearAuthData = (): void => {
    // 清除状态
    loginStatus.value = LoginStatus.NOT_LOGGED_IN;
    userInfo.value = null;
    accessToken.value = '';
    userMenus.value = [];
    userPermissions.value = [];
    userRoles.value = [];

    // 清除 Token
    TokenManager.removeToken();

    // 持久化清除由 pinia-plugin-persistedstate 自动处理
  };

  /**
   * 从菜单中提取权限
   * @param menus 菜单列表
   */
  const extractPermissionsFromMenus = (menus: MenuRecord[]): string[] => {
    const permissions: string[] = [];
    
    const extractFromMenu = (menu: MenuRecord) => {
      // 添加菜单路径作为权限
      if (menu.path) {
        permissions.push(menu.path);
      }
      
      // 添加菜单名称作为权限
      if (menu.name) {
        permissions.push(menu.name);
      }
      
      // 递归处理子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children.forEach(extractFromMenu);
      }
    };
    
    menus.forEach(extractFromMenu);
    return [...new Set(permissions)]; // 去重
  };

  /**
   * 初始化认证状态（应用启动时调用）
   */
  const initAuth = async (): Promise<void> => {
    try {
      // 等待一个微任务周期，确保持久化插件已经恢复数据
      await new Promise(resolve => setTimeout(resolve, 0));

      // 调试信息：检查状态恢复情况
      console.log('🔍 initAuth - 检查状态恢复情况:');
      console.log('accessToken:', accessToken.value);
      console.log('userInfo:', userInfo.value);
      console.log('loginStatus:', loginStatus.value);
      console.log('userMenus length:', userMenus.value.length);

      // 检查 localStorage 中的数据
      const localStorageData = localStorage.getItem('auth-store');
      console.log('localStorage auth-store:', localStorageData);

      // 由于使用了 pinia-plugin-persistedstate，状态已经自动从 localStorage 恢复
      // 只需要检查是否有有效的认证状态

      if (accessToken.value && userInfo.value) {
        // 如果有 token 和用户信息，验证 token 有效性
        const isValid = await validateToken();
        if (isValid) {
          // Token 有效，设置登录状态
          loginStatus.value = LoginStatus.LOGGED_IN;

          // 如果没有菜单数据，重新获取
          if (userMenus.value.length === 0) {
            await getUserMenus();
          }
        } else {
          // Token 无效，清除认证数据
          clearAuthData();
          loginStatus.value = LoginStatus.NOT_LOGGED_IN;
        }
      } else {
        // 没有认证数据，检查是否有独立存储的 token
        const token = TokenManager.getToken();
        if (token && token !== accessToken.value) {
          accessToken.value = token;
          // 验证 token 并获取用户信息
          const isValid = await validateToken();
          if (isValid) {
            await Promise.all([
              getCurrentUser(),
              getUserMenus()
            ]);
            loginStatus.value = LoginStatus.LOGGED_IN;
          } else {
            clearAuthData();
            loginStatus.value = LoginStatus.NOT_LOGGED_IN;
          }
        } else {
          // 没有任何认证信息
          loginStatus.value = LoginStatus.NOT_LOGGED_IN;
        }
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error);
      clearAuthData();
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
    } finally {
      // 无论成功还是失败，都标记为已初始化
      isInitialized.value = true;
    }
  };

  return {
    // 状态
    loginStatus,
    userInfo,
    accessToken,
    userMenus,
    userPermissions,
    userRoles,
    isInitialized,

    // 计算属性
    isLoggedIn,
    isLoggingIn,
    isTokenExpired,
    displayName,
    userAvatar,
    hasPermission,
    hasRole,

    // 方法
    login,
    logout,
    refreshToken,
    getCurrentUser,
    getUserMenus,
    validateToken,
    setAuthData,
    clearAuthData,
    initAuth
  };
}, {
  // 持久化配置
  persist: {
    key: 'auth-store',
    storage: localStorage,
    pick: [
      'loginStatus',
      'userInfo',
      'accessToken',
      'userMenus',
      'userPermissions',
      'userRoles'
    ],
    debug: true,
    beforeHydrate: (ctx) => {
      console.log('🔄 [Persist] 准备恢复数据:', ctx.store.$id);
    },
    afterHydrate: (ctx) => {
      console.log('✅ [Persist] 数据恢复完成:', ctx.store.$id);
      console.log('恢复的状态:', ctx.store.$state);
    }
  }
});
