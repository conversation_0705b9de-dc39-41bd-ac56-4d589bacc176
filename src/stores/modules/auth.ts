/**
 * 认证状态管理 Store
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { AuthApi } from '@/api/auth';
import { TokenManager } from '@/utils/request';
import { AuthStorageManager } from '@/utils/storage';
import type {
  UserLoginReq,
  AuthRecord,
  UserLoginRecord,
  MenuRecord,
  LoginStatusType
} from '@/types';
import { LoginStatus } from '@/types';

export const useAuthStore = defineStore('auth', () => {
  // ===== 状态定义 =====
  const loginStatus = ref<LoginStatusType>(LoginStatus.NOT_LOGGED_IN);
  const userInfo = ref<UserLoginRecord | null>(null);
  const accessToken = ref<string>('');
  const userMenus = ref<MenuRecord[]>([]);
  const userPermissions = ref<string[]>([]);
  const userRoles = ref<string[]>([]);

  // ===== 计算属性 =====
  const isLoggedIn = computed(() => loginStatus.value === LoginStatus.LOGGED_IN);
  const isLoggingIn = computed(() => loginStatus.value === LoginStatus.LOGGING_IN);
  const isTokenExpired = computed(() => loginStatus.value === LoginStatus.TOKEN_EXPIRED);
  
  // 获取用户昵称或用户名
  const displayName = computed(() => {
    if (!userInfo.value) return '';
    return userInfo.value.nickname || userInfo.value.username || '未知用户';
  });

  // 获取用户头像
  const userAvatar = computed(() => {
    return userInfo.value?.avatar || '';
  });

  // 检查是否有特定权限
  const hasPermission = computed(() => {
    return (permission: string) => {
      return userPermissions.value.includes(permission);
    };
  });

  // 检查是否有特定角色
  const hasRole = computed(() => {
    return (role: string) => {
      return userRoles.value.includes(role);
    };
  });

  // ===== Actions =====
  
  /**
   * 用户登录
   * @param loginData 登录参数
   */
  const login = async (loginData: UserLoginReq): Promise<boolean> => {
    try {
      loginStatus.value = LoginStatus.LOGGING_IN;
      
      // 调用登录API
      const authRecord = await AuthApi.login(loginData);
      
      // 保存认证信息
      await setAuthData(authRecord);
      
      loginStatus.value = LoginStatus.LOGGED_IN;
      message.success('登录成功');
      
      return true;
    } catch (error) {
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      console.error('登录失败:', error);
      message.error('登录失败，请检查用户名和密码');
      return false;
    }
  };

  /**
   * 用户登出
   */
  const logout = async (): Promise<void> => {
    try {
      loginStatus.value = LoginStatus.LOGGING_OUT;
      
      // 调用登出API
      await AuthApi.logout();
    } catch (error) {
      console.warn('登出API调用失败:', error);
    } finally {
      // 无论API是否成功，都要清除本地状态
      clearAuthData();
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      message.success('已退出登录');
    }
  };

  /**
   * 刷新Token
   */
  const refreshToken = async (): Promise<boolean> => {
    try {
      const authRecord = await AuthApi.refreshToken();
      await setAuthData(authRecord);
      loginStatus.value = LoginStatus.LOGGED_IN;
      return true;
    } catch (error) {
      console.error('Token刷新失败:', error);
      loginStatus.value = LoginStatus.TOKEN_EXPIRED;
      clearAuthData();
      return false;
    }
  };

  /**
   * 获取当前用户信息
   */
  const getCurrentUser = async (): Promise<boolean> => {
    try {
      const user = await AuthApi.getCurrentUser();
      userInfo.value = user;
      return true;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return false;
    }
  };

  /**
   * 获取用户菜单权限
   */
  const getUserMenus = async (): Promise<boolean> => {
    try {
      const menus = await AuthApi.getUserMenus();
      userMenus.value = menus;
      return true;
    } catch (error) {
      console.error('获取用户菜单失败:', error);
      return false;
    }
  };

  /**
   * 验证Token有效性
   */
  const validateToken = async (): Promise<boolean> => {
    const token = TokenManager.getToken();
    if (!token) {
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      return false;
    }

    try {
      const isValid = await AuthApi.validateToken();
      if (isValid) {
        loginStatus.value = LoginStatus.LOGGED_IN;
        return true;
      } else {
        loginStatus.value = LoginStatus.TOKEN_EXPIRED;
        clearAuthData();
        return false;
      }
    } catch (error) {
      console.error('Token验证失败:', error);
      loginStatus.value = LoginStatus.TOKEN_EXPIRED;
      clearAuthData();
      return false;
    }
  };

  /**
   * 设置认证数据
   * @param authRecord 认证记录
   */
  const setAuthData = async (authRecord: AuthRecord): Promise<void> => {
    // 保存用户信息到状态
    userInfo.value = authRecord.userInfo;

    // 保存Token到状态和存储
    accessToken.value = authRecord.authInfo.accessToken;
    TokenManager.setToken(authRecord.authInfo.accessToken);

    // 保存菜单权限到状态
    userMenus.value = authRecord.authInfo.menus;

    // 提取角色信息
    const roles = authRecord.userInfo.roles.map(role => role.key);
    userRoles.value = roles;

    // 提取权限信息（从菜单中提取）
    const permissions = extractPermissionsFromMenus(authRecord.authInfo.menus);
    userPermissions.value = permissions;

    // 使用AuthStorageManager保存到localStorage
    AuthStorageManager.setUserInfo(authRecord.userInfo);
    AuthStorageManager.setUserMenus(authRecord.authInfo.menus);
    AuthStorageManager.setUserRoles(roles);
    AuthStorageManager.setUserPermissions(permissions);
    AuthStorageManager.setLoginStatus(LoginStatus.LOGGED_IN);
  };

  /**
   * 清除认证数据
   */
  const clearAuthData = (): void => {
    // 清除状态
    userInfo.value = null;
    accessToken.value = '';
    userMenus.value = [];
    userPermissions.value = [];
    userRoles.value = [];

    // 清除localStorage中的认证数据
    AuthStorageManager.clearAuthData();
  };

  /**
   * 从菜单中提取权限
   * @param menus 菜单列表
   */
  const extractPermissionsFromMenus = (menus: MenuRecord[]): string[] => {
    const permissions: string[] = [];
    
    const extractFromMenu = (menu: MenuRecord) => {
      // 添加菜单路径作为权限
      if (menu.path) {
        permissions.push(menu.path);
      }
      
      // 添加菜单名称作为权限
      if (menu.name) {
        permissions.push(menu.name);
      }
      
      // 递归处理子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children.forEach(extractFromMenu);
      }
    };
    
    menus.forEach(extractFromMenu);
    return [...new Set(permissions)]; // 去重
  };

  /**
   * 初始化认证状态（应用启动时调用）
   */
  const initAuth = async (): Promise<void> => {
    try {
      // 优先从localStorage获取缓存的认证信息
      const cachedToken = AuthStorageManager.getAccessToken();
      const cachedUserInfo = AuthStorageManager.getUserInfo<UserLoginRecord>();
      const cachedMenus = AuthStorageManager.getUserMenus<MenuRecord[]>();
      const cachedRoles = AuthStorageManager.getUserRoles();
      const cachedPermissions = AuthStorageManager.getUserPermissions();
      const cachedLoginStatus = AuthStorageManager.getLoginStatus();

      if (cachedToken && cachedUserInfo) {
        // 恢复状态
        accessToken.value = cachedToken;
        userInfo.value = cachedUserInfo;
        userMenus.value = cachedMenus || [];
        userRoles.value = cachedRoles;
        userPermissions.value = cachedPermissions;

        // 设置登录状态
        if (cachedLoginStatus === LoginStatus.LOGGED_IN) {
          loginStatus.value = LoginStatus.LOGGED_IN;
        }

        // 验证token有效性（可选，避免每次启动都验证）
        // const isValid = await validateToken();
        // if (!isValid) {
        //   clearAuthData();
        //   loginStatus.value = LoginStatus.NOT_LOGGED_IN;
        // }
      } else {
        // 没有缓存数据，检查是否有token
        const token = TokenManager.getToken();
        if (token) {
          accessToken.value = token;
          // 验证token并获取用户信息
          const isValid = await validateToken();
          if (isValid) {
            await Promise.all([
              getCurrentUser(),
              getUserMenus()
            ]);
          } else {
            clearAuthData();
          }
        }
      }
    } catch (error) {
      console.error('初始化认证状态失败:', error);
      clearAuthData();
      loginStatus.value = LoginStatus.NOT_LOGGED_IN;
    }
  };

  return {
    // 状态
    loginStatus,
    userInfo,
    accessToken,
    userMenus,
    userPermissions,
    userRoles,
    
    // 计算属性
    isLoggedIn,
    isLoggingIn,
    isTokenExpired,
    displayName,
    userAvatar,
    hasPermission,
    hasRole,
    
    // 方法
    login,
    logout,
    refreshToken,
    getCurrentUser,
    getUserMenus,
    validateToken,
    setAuthData,
    clearAuthData,
    initAuth
  };
});
