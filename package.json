{"name": "combo-opm-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "4.2.6", "axios": "^1.10.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.0.3", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "less": "^4.3.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.11.0+sha256.a69e9cb077da419d47d18f1dd52e207245b29cac6e076acedbeb8be3b1a67bd7"}