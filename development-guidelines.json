{"developmentGuidelines": {"name": "Vue 3 + TypeScript Development Standards", "version": "1.0.0", "description": "为Vue 3和TypeScript项目制定的开发规范，旨在提高代码质量、可维护性和可扩展性。", "principles": [{"id": "avoid-hardcoding", "title": "避免硬编码", "description": "所有常量、配置、API地址和魔法字符串都应集中管理，避免直接写入业务逻辑。", "details": {"configurationManagement": {"path": "src/config/", "usage": "存放应用级别的配置，如API基础URL、通用设置等。", "example": "src/config/api.ts, src/config/app.ts"}, "environmentVariables": {"mechanism": "利用构建工具（如Vite的import.meta.env或Vue CLI的process.env）管理环境变量。", "example": ".env, .env.production"}, "constantsAndEnums": {"path": "src/constants/", "usage": "存放不可变的常量值和枚举类型，代替硬编码的字符串或数字。", "example": "src/constants/roles.ts (enum UserRole { Admin = 'admin' }), src/constants/status.ts"}}}, {"id": "ts-file-organization", "title": "TS文件分类和职责分离", "description": "根据功能和类型划分TypeScript文件，避免一个文件承担过多职责，特别是类型定义。", "details": {"globalTypes": {"path": "src/types/", "usage": "存放全局或跨模块的接口（interface）和类型别名（type）。", "example": "src/types/common.ts, src/types/user.ts"}, "enums": {"path": "src/enums/", "usage": "存放全局枚举定义。", "example": "src/enums/ErrorCode.ts"}, "utilities": {"path": "src/utils/", "usage": "存放通用工具函数，如日期处理、表单校验、数据转换等，应保持函数无副作用且单一职责。", "example": "src/utils/date.ts, src/utils/validation.ts"}, "apiServices": {"path": "src/api/", "usage": "集中管理API请求的定义和封装，每个文件对应一个或一组相关联的API服务。", "example": "src/api/userService.ts, src/api/productService.ts"}, "stateManagement": {"path": "src/stores/ (for Pinia) or src/store/ (for Vuex)", "usage": "存放状态管理模块，每个模块对应一个清晰的业务领域。", "example": "src/stores/user.ts, src/stores/cart.ts"}, "composables": {"path": "src/composables/", "usage": "存放可复用的组合式函数，封装组件的逻辑和状态，提高内聚性。", "example": "src/composables/usePagination.ts, src/composables/useAuth.ts"}, "componentLocalTypes": {"path": "与组件同级或在组件内部的<script setup>", "usage": "组件特有的类型定义可以放在组件文件内部或其同级的types.ts文件中。"}}}, {"id": "high-cohesion-low-coupling-srp", "title": "高内聚、低耦合、职责单一（SRP）、易拓展和维护", "description": "设计代码时遵循这些核心原则，确保模块化、可测试和未来可变性。", "details": {"componentDesign": {"singleResponsibility": "每个组件应只做一件事，避免创建'巨石'组件。", "propsDriven": "组件间通信主要通过props自上而下传递数据。", "eventEmitting": "通过emit事件自下而上传递信息和触发操作。", "slotsUsage": "利用插槽提高组件的灵活性和内容分发能力。", "namingConventions": "组件命名应清晰、有意义（PascalCase），反映其功能。"}, "compositionApiUtilization": {"purpose": "将组件逻辑拆分为独立的、可复用的逻辑单元（composables）。", "benefits": "增强代码内聚性，提高可复用性，简化组件逻辑，降低耦合。", "example": "将表单验证逻辑封装为useFormValidation，而不是写在组件内部。"}, "separationOfConcerns": {"dataLayer": "API请求和数据处理逻辑应从组件中抽离到api服务或store中。", "uiLayer": "组件主要负责UI渲染和用户交互，不包含复杂的业务逻辑或数据请求。"}}}, {"id": "use-pnpm", "title": "使用pnpm添加依赖", "description": "统一项目依赖管理工具为pnpm，以优化包安装速度和磁盘空间使用。", "details": {"command": "`pnpm add <package-name>` 或 `pnpm install`", "enforcement": "建议在项目中配置.npmrc (engine-strict=true) 和 package.json (engines.pnpm) 来强制使用pnpm。"}}, {"id": "simplicity-and-maintainability", "title": "避免过度设计，追求简单易维护", "description": "遵循KISS（Keep It Simple, Stupid）原则，避免不必要的复杂抽象，优先考虑代码的直观性和可读性。", "details": {"onDemandAbstraction": "只有当某块逻辑确实被多个地方复用或有明确的未来扩展需求时，才进行抽象。", "incrementalEnhancement": "从简单实现开始，根据需求演进逐步重构和优化。", "codeClarity": "使用清晰、有意义的变量/函数名，编写简洁的代码，并辅以必要的注释（避免过度注释）。", "codeReview": "定期进行代码审查，以发现并简化过度设计或复杂化的代码。"}}]}}