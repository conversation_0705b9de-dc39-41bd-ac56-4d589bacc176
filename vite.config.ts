import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import type { IncomingMessage, ServerResponse } from "http";

// https://vite.dev/config/
export default defineConfig(({ mode, command }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), "");

  // 从环境变量获取后端配置
  const backendHost = env.VITE_BACKEND_HOST || "localhost";
  const backendPort = env.VITE_BACKEND_PORT || "1001";
  const apiProtocol = env.VITE_API_PROTOCOL || "http";
  const devServerPort = Number(env.VITE_DEV_SERVER_PORT) || 3000;
  // 构建后端目标地址
  const backendTarget = `${apiProtocol}://${backendHost}:${backendPort}`;
  const isProduction = command === "build";

  console.log(`🔧 Vite 配置 - 模式: ${mode}, 命令: ${command}`);
  if (!isProduction) {
    console.log(`🔧 开发服务器代理: ${backendTarget}`);
  }
  return {
    plugins: [vue()],
    server: {
      port: devServerPort,
      open: true,
      // 代理配置
      proxy: {
        "/api": {
          target: backendTarget,
          changeOrigin: true,
          secure: apiProtocol === "https",
          configure: (proxy: any, _options: any) => {
            proxy.on("error", (err: Error, _req: IncomingMessage, _res: ServerResponse) => {
              console.log("🚨 代理错误:", err.message);
              console.log("🔍 检查后端服务是否在", backendTarget, "上运行");
            });

            proxy.on("proxyReq", (_proxyReq: any, req: IncomingMessage, _res: ServerResponse) => {
              console.log(
                "📤 发送请求:",
                req.method,
                req.url,
                "->",
                backendTarget + req.url
              );
            });

            proxy.on("proxyRes", (proxyRes: IncomingMessage, req: IncomingMessage, _res: ServerResponse) => {
              console.log("📥 接收响应:", (proxyRes as any).statusCode, req.url);
            });
          },
        },
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
  };
});
