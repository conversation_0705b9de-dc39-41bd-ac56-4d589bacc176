# 陈述性记忆

## 高价值记忆（评分 ≥ 7）

- 2025/06/26 15:21 START
用户需要重新设计Dashboard.vue工作台页面，要求：1)使用Ant Design Vue组件和项目现有主题系统 2)实现4种布局模式切换(上中下、顶部-侧边-通栏、顶部-侧边、侧边) 3)现代化美观设计 4)组件化架构 5)TypeScript类型安全。项目已有完整的主题系统(ThemeProvider、主题切换、Design Token)和路由架构。 --tags dashboard redesign layout-switching ant-design-vue theme-system
--tags #其他 #评分:8 #有效期:长期
- END



- 2025/06/26 15:34 START
成功重新设计并实现了Dashboard.vue工作台页面，包括：1)创建了完整的类型定义系统(dashboard.ts) 2)实现了组合式函数架构(useDashboard.ts) 3)开发了可复用组件(StatCard、QuickActions、RecentActivities、LayoutSwitcher) 4)实现了4种布局模式切换(上中下、顶部-侧边-通栏、顶部-侧边、侧边) 5)使用Ant Design Vue组件和项目主题系统 6)现代化美观设计，支持响应式和深色主题 7)TypeScript类型安全，组件化架构。项目成功启动在localhost:3001。 --tags dashboard-redesign completed layout-switching ant-design-vue modern-ui typescript
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/26 16:31 START
成功实现了MainLayout.vue主布局组件，基于iBanko Dashboard设计系统：1)完整的侧边栏导航(Logo、菜单、系统状态) 2)顶部导航栏(折叠按钮、面包屑、搜索、主题切换、通知、用户菜单) 3)主内容区域(路由视图、页面切换动画) 4)底部信息栏 5)通知抽屉 6)响应式设计和深色主题支持 7)基于设计系统的完整样式实现 8)TypeScript类型安全。修复了主题系统导入问题。 --tags mainlayout ibanko-design-system ant-design-vue responsive dark-theme typescript
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/26 16:55 START
成功优化MainLayout.vue组件UI设计：1)整体布局圆润化-增加12px圆角、柔和阴影、流畅动画效果 2)Header响应式优化-使用a-space统一间距、搜索框最大320px、移动端自适应 3)用户信息垂直对齐修正-使用flexbox确保文字与头像对齐、优化line-height 4)用户菜单间距优化-图标文字间距10px、菜单项padding 12px 5)深色主题完美支持 6)响应式设计验证通过。所有交互状态(hover、focus)正常，视觉层次清晰。 --tags mainlayout-optimization ui-design responsive-design dark-theme ant-design-vue
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 14:30 START
重新设计MainLayout.vue的搜索框和按钮：1)修复搜索框内容缺失问题，添加完整的a-input-search组件 2)统一圆角设计语言，将搜索按钮从10px调整为12px圆角 3)增强搜索交互体验，添加自定义enterButton模板使用SearchOutlined图标 4)优化样式层次，使用渐变背景、透明边框、柔和阴影 5)完善深色主题支持，调整颜色和对比度 6)改进响应式设计，保持320px/200px宽度切换 7)增加清除按钮和占位符的样式优化 8)确保与iBanko设计系统的一致性。搜索框现在具有更好的视觉反馈和用户体验。 --tags search-redesign mainlayout ui-optimization design-consistency ant-design-vue
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 16:21 START
MainLayout.vue 完整设计规范：

## 架构设计
- 基于 Ant Design Vue 的 Layout 组件系统
- 采用侧边栏+主内容区的经典布局模式
- 支持响应式设计，移动端自适应
- 完全集成主题系统，使用 CSS 变量实现动态主题切换

## 布局结构
1. 侧边栏 (a-layout-sider): 220px宽度，折叠时80px
2. 主内容区 (a-layout): 包含头部、内容、底部
3. 移动端遮罩层: 小屏幕时的半透明遮罩

## 核心特性
- Logo区域：渐变背景+渐变文字+光泽动画效果
- 导航菜单：圆角设计+悬停动画+选中状态指示器
- 顶部导航：搜索框+通知+主题切换+用户信息
- 内容区域：圆角卡片+顶部装饰条+悬停效果
- 响应式断点：992px (lg)、768px、576px

## 动画系统
- 统一使用 cubic-bezier(0.25, 0.46, 0.45, 0.94) 缓动函数
- 悬停效果：translateY(-2px) + 阴影增强
- 路由切换：fade-slide 过渡动画
- Logo光泽：0.6s 从左到右扫过效果

## 主题集成
- 完全使用 CSS 变量 (--theme-*)
- 支持默认/暗色/紧凑/自定义主题
- 自动适配主题色渐变效果 --tags MainLayout Vue设计规范 布局系统 主题集成 响应式设计
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 16:23 START
MainLayout.vue 设计文档整理完成：

## 文档体系
1. **设计规范文档** (MainLayout-Design-Specification.md)
   - 完整的设计系统说明
   - 架构设计和技术实现
   - 响应式设计和主题集成
   - 维护指南和使用示例

2. **结构化数据** (MainLayout-Structure.json)
   - JSON格式的组件结构
   - 详细的样式规范
   - 响应式断点配置
   - 动画和主题变量定义
   - 菜单结构和CSS类名

3. **快速参考** (MainLayout-Quick-Reference.md)
   - 核心特性一览
   - 关键尺寸和变量
   - 响应式行为说明
   - 使用要点和检查清单

## 文档特点
- 结构化：分层次的信息组织
- 可读性：人类友好的Markdown格式
- 机器可读：JSON格式便于AI理解
- 实用性：包含使用指南和最佳实践
- 完整性：覆盖设计、开发、维护各方面

## 应用价值
- 新团队成员快速上手
- 设计系统一致性保证
- 代码维护和扩展指导
- AI辅助开发的知识库 --tags 文档整理 设计系统 MainLayout 知识管理 团队协作
--tags #最佳实践 #评分:8 #有效期:长期
- END

- 2025/06/27 16:34 START
成功为Combo-OPM项目封装了完整的axios请求库：

## 核心架构
1. **类型系统** (src/types/api.ts): 完整的API类型定义，包括请求/响应结构、分页、错误处理等
2. **请求封装** (src/utils/request.ts): 基于axios的HttpRequest类，支持拦截器、token管理、loading管理、错误处理
3. **API服务层**: 按业务模块分离的API服务(AuthApi、UserApi、CustomerApi、RoleApi、MenuApi)

## 核心特性
- **自动token管理**: TokenManager类处理token存储、获取、清除
- **统一错误处理**: HTTP错误和业务错误的自动处理和提示
- **Loading管理**: LoadingManager类控制全局loading状态
- **类型安全**: 完整的TypeScript类型支持
- **配置化**: 支持loading、错误提示、成功提示等配置
- **文件上传**: 内置文件上传支持

## 使用模式
- 基于类的静态方法设计，易于使用和维护
- 支持链式配置，灵活的请求选项
- 自动处理认证流程和权限控制
- 完整的业务API封装(用户、客户、角色、菜单管理)

## 配置完善
- 更新了环境变量配置(.env.development)
- 创建了详细的使用文档和示例代码
- 提供了最佳实践指南

这个封装为项目提供了企业级的API请求解决方案，支持现代前端开发的各种需求。 --tags axios封装 API架构 TypeScript 企业级开发 请求管理
--tags #最佳实践 #流程管理 #评分:8 #有效期:长期
- END

- 2025/06/27 17:06 START
API架构优化的编码习惯和最佳实践：

## 1. API接口地址常量化
- 所有API接口地址统一定义在 `src/constants/api.ts` 中
- 按业务模块分组：AUTH_API、USER_API、CUSTOMER_API、ROLE_API、MENU_API
- 支持动态参数的接口使用函数形式：`DETAIL: (id: number) => `/users/${id}``
- 导出统一的 API_ENDPOINTS 对象便于管理

## 2. 类型定义模块化分离
- 基础通用类型保留在 `src/types/api.ts`（ApiResponse、PageParams、PageResult等）
- 业务相关类型按模块分离：
  - `src/types/auth.ts` - 认证相关类型
  - `src/types/user.ts` - 用户管理类型
  - `src/types/customer.ts` - 客户管理类型
  - `src/types/role.ts` - 角色管理类型
  - `src/types/menu.ts` - 菜单管理类型
- 统一在 `src/types/index.ts` 中导出所有类型

## 3. 避免重复类型定义
- API文件中不再定义业务类型，直接从types模块导入
- 每个类型只在一个地方定义，避免维护混乱
- 使用TypeScript的类型导入：`import type { ... } from '@/types'`

## 4. API文件结构优化
- 导入顺序：工具类 -> 常量 -> 类型定义
- 移除API文件中的重复接口定义
- 使用常量替代硬编码的接口路径
- 保持API类的静态方法结构

## 5. 类型安全和一致性
- 使用 `as const` 确保常量类型推断
- 导出类型值联合类型：`export type StatusValue = typeof Status[keyof typeof Status]`
- 枚举改为const对象避免TypeScript配置问题

这种架构确保了代码的可维护性、类型安全性和开发效率。 --tags API架构 类型定义 编码规范 模块化 最佳实践
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/27 17:23 START
Vue.js + TypeScript 项目编码规范标准 v2.0

## 核心编码规范

### 1. API架构设计原则
**强制要求**：
- 所有API接口地址必须定义在 `src/constants/api.ts` 中，禁止硬编码
- 按业务模块分组：AUTH_API、USER_API、CUSTOMER_API、ROLE_API、MENU_API
- 动态参数接口使用函数形式：`DETAIL: (id: number) => \`/users/\${id}\``
- 导出统一的 API_ENDPOINTS 对象

### 2. 类型定义模块化规范
**强制要求**：
- 基础通用类型保留在 `src/types/api.ts`（ApiResponse、PageParams、PageResult等）
- 业务类型按模块严格分离到独立文件
- 统一在 `src/types/index.ts` 中导出所有类型
- API文件中禁止定义业务类型，只能导入使用

### 3. 重复定义消除原则
**强制要求**：
- 每个类型只能在一个地方定义，严禁重复
- API文件必须从types模块导入类型，不得自定义
- 使用 `import type { ... } from '@/types'` 导入类型

### 4. API文件结构标准
**强制要求**：
- 导入顺序：工具类 -> 常量 -> 类型定义
- 所有接口路径必须使用常量，严禁硬编码字符串
- 保持API类的静态方法结构
- 方法命名遵循RESTful规范

### 5. TypeScript类型安全要求
**强制要求**：
- 使用 `as const` 确保常量类型推断
- 枚举改为const对象避免配置问题
- 导出类型值联合类型：`export type StatusValue = typeof Status[keyof typeof Status]`
- 所有API方法必须有完整的类型注解

## 检查清单
在每次API开发时必须验证：
□ 是否使用了API常量而非硬编码路径
□ 是否存在重复的类型定义
□ 类型是否从统一的types模块导入
□ 是否遵循了模块化分离原则
□ TypeScript类型检查是否通过

## 违规处理
发现违反以上规范的代码必须立即重构，不得合并到主分支。 --tags 编码规范 API架构 TypeScript 模块化 最佳实践 强制标准
--tags #最佳实践 #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/27 17:41 START
完成了Combo-OPM项目的登录页面功能完善和Pinia状态管理集成：

## 核心实现
1. **Pinia状态管理**: 安装并配置了Pinia，创建了完整的认证状态管理(useAuthStore)和用户状态管理(useUserStore)
2. **登录页面集成**: 更新Login.vue使用真实API和Pinia状态管理，支持用户名密码登录和SMS验证码登录
3. **路由守卫升级**: 更新路由守卫使用Pinia状态而非localStorage进行认证检查
4. **Dashboard用户信息**: 集成用户信息显示和退出登录功能
5. **应用初始化**: 在App.vue中添加认证状态初始化逻辑

## 技术特性
- **完整的Token管理**: 自动存储、验证、刷新Token机制
- **状态持久化**: 用户登录状态在页面刷新后保持
- **权限管理**: 支持角色和权限检查
- **错误处理**: 完善的API错误处理和用户提示
- **类型安全**: 完整的TypeScript类型定义

## 文件结构
- src/stores/index.ts - Pinia入口文件
- src/stores/modules/auth.ts - 认证状态管理
- src/stores/modules/user.ts - 用户状态管理
- src/views/Login.vue - 登录页面(已集成API)
- src/views/Dashboard.vue - 仪表板(显示用户信息)
- docs/login-testing.md - 测试指南

## 使用方式
- 使用pnpm安装依赖(记住用户偏好)
- 开发服务器: pnpm dev (http://localhost:3001)
- 支持用户名密码登录和SMS验证码登录
- 完整的路由守卫和权限控制

这为后续的用户管理、权限控制和业务功能开发奠定了坚实基础。 --tags 登录功能 Pinia状态管理 Vue.js认证 路由守卫 用户管理
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/27 18:14 START
Vue 3 + TypeScript 开发规范 v1.0.0：1) 避免硬编码 - 所有常量、API地址、配置集中在src/constants/、src/config/管理，使用环境变量和枚举；2) TS文件分类 - 全局类型src/types/，工具函数src/utils/，API服务src/api/，状态管理src/stores/，组合函数src/composables/；3) 高内聚低耦合 - 组件单一职责，通过props/emit通信，使用Composition API拆分逻辑为composables，分离数据层和UI层；4) 使用pnpm管理依赖；5) 避免过度设计，追求简单易维护，按需抽象，增量优化 --tags 开发规范 Vue3 TypeScript 项目标准
--tags #工具使用 #评分:8 #有效期:长期
- END

- 2025/06/27 18:16 START
用户补充的开发规范细节：1) API类型定义放在src/types/api/子目录下按模块分类；2) 组件特有类型定义创建同级types.ts文件；3) 业务常量按模块分类到子目录（如src/constants/user/、src/constants/order/）；4) 需要统一的错误处理机制和错误类型定义 --tags 开发规范 API类型 组件类型 常量分类 错误处理
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/29 13:06 START
用户需要Vue 3 + TypeScript + Ant Design Vue项目的组件封装编码规范，并偏好简化复杂prop/method接口的组件设计模式。字符串字面量（如size值）应转换为可管理的常量，并进行分类组织，以提高可维护性。 --tags 组件封装 编码规范 Vue3 TypeScript 常量管理
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/29 13:18 START
User prefers Vue 3 + TypeScript + Ant Design Vue projects to have comprehensive string constant management - all string literals used across components (like action types 'edit', 'delete', event types, status values, etc.) should be defined as constants in centralized files to enable easy global changes and prevent inconsistencies across multiple components. --tags 字符串常量管理 Vue3 TypeScript 组件开发 编码规范
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/29 13:22 START
User prefers Vue 3 + TypeScript + Ant Design Vue projects to have comprehensive string constant management - all string literals used across components (like action types 'edit', 'delete', event types, status values, etc.) should be defined as constants in centralized files to enable easy global changes and prevent inconsistencies across multiple components. --tags 字符串常量管理 Vue3 TypeScript 组件规范
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/29 13:23 START
User prefers Vue 3 + TypeScript + Ant Design Vue projects to have comprehensive string constant management - all string literals used across components (like action types 'edit', 'delete', event types, status values, etc.) should be defined as constants in centralized files to enable easy global changes and prevent inconsistencies across multiple components. --tags 字符串常量管理 组件封装 Vue3 TypeScript 编码规范
--tags #其他 #评分:8 #有效期:长期
- END

- 2025/06/29 13:30 START
Vue 3 + TypeScript + Ant Design Vue 组件封装编码规范 v3.0 已创建完成，位于 docs/component-coding-standards.md。此规范为项目强制执行标准，包含：1)字符串常量化强制要求(业务相关必须常量化，Vue/HTML/CSS标准保持原样) 2)完整的TypeScript类型定义 3)详细注释规范 4)BEM样式规范 5)组件结构标准 6)完整组件示例。AI和PromptX必须严格遵循此规范进行组件开发。 --tags 组件封装规范 Vue3 TypeScript 编码标准 字符串常量化 强制执行
--tags #其他 #评分:8 #有效期:长期
- END