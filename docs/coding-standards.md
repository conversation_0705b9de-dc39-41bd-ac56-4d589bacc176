# Vue.js + TypeScript 项目编码规范标准 v2.0

> **重要提示**：本规范为强制执行标准，所有代码必须严格遵循，违规代码不得合并到主分支。

## 📋 目录

- [1. API架构设计原则](#1-api架构设计原则)
- [2. 类型定义模块化规范](#2-类型定义模块化规范)
- [3. 重复定义消除原则](#3-重复定义消除原则)
- [4. API文件结构标准](#4-api文件结构标准)
- [5. TypeScript类型安全要求](#5-typescript类型安全要求)
- [6. 代码检查清单](#6-代码检查清单)
- [7. 示例对比](#7-示例对比)

---

## 1. API架构设计原则

### 🔒 强制要求

#### 1.1 接口地址常量化
- **所有API接口地址必须定义在 `src/constants/api.ts` 中**
- **严禁在API文件中使用硬编码字符串路径**
- 按业务模块分组管理

```typescript
// ✅ 正确示例
export const USER_API = {
  LIST: '/users',
  PAGE: '/users/page',
  DETAIL: (id: number) => `/users/${id}`,
  CREATE: '/users',
  UPDATE: (id: number) => `/users/${id}`,
  DELETE: (id: number) => `/users/${id}`,
} as const;
```

#### 1.2 模块分组规范
必须按以下业务模块分组：
- `AUTH_API` - 认证相关接口
- `USER_API` - 用户管理接口
- `CUSTOMER_API` - 客户管理接口
- `ROLE_API` - 角色管理接口
- `MENU_API` - 菜单管理接口

#### 1.3 动态参数处理
动态参数接口必须使用函数形式：

```typescript
// ✅ 正确
DETAIL: (id: number) => `/users/${id}`,
UPDATE: (id: number) => `/users/${id}`,

// ❌ 错误
DETAIL: '/users/:id',
```

---

## 2. 类型定义模块化规范

### 🔒 强制要求

#### 2.1 类型文件分离
- **基础通用类型** → `src/types/api.ts`
- **业务特定类型** → 按模块分离到独立文件

```
src/types/
├── api.ts          # 基础类型：ApiResponse, PageParams, PageResult
├── auth.ts         # 认证相关类型
├── user.ts         # 用户管理类型
├── customer.ts     # 客户管理类型
├── role.ts         # 角色管理类型
├── menu.ts         # 菜单管理类型
└── index.ts        # 统一导出
```

#### 2.2 类型导出规范
所有类型必须在 `src/types/index.ts` 中统一导出：

```typescript
// src/types/index.ts
export * from './api';
export * from './auth';
export * from './user';
export * from './customer';
export * from './role';
export * from './menu';
```

---

## 3. 重复定义消除原则

### 🔒 强制要求

#### 3.1 单一定义原则
- **每个类型只能在一个地方定义**
- **严禁在API文件中重复定义业务类型**
- **必须从types模块导入类型**

```typescript
// ❌ 错误：在API文件中定义类型
export interface UserQueryParams {
  username?: string;
  status?: number;
}

// ✅ 正确：从types模块导入
import type { UserQueryParams } from '@/types';
```

#### 3.2 导入规范
使用类型导入语法：

```typescript
// ✅ 正确
import type { 
  PageResult,
  UserRecord,
  UserQueryParams,
  UserFormData
} from '@/types';
```

---

## 4. API文件结构标准

### 🔒 强制要求

#### 4.1 导入顺序
严格按以下顺序导入：

```typescript
// 1. 工具类导入
import request from '@/utils/request';

// 2. 常量导入
import { USER_API } from '@/constants/api';

// 3. 类型导入
import type { 
  PageResult,
  UserRecord,
  UserQueryParams,
  UserFormData
} from '@/types';
```

#### 4.2 方法实现规范
- 所有接口路径必须使用常量
- 保持API类的静态方法结构
- 方法命名遵循RESTful规范

```typescript
// ✅ 正确示例
export class UserApi {
  static async getUserPage(params: UserQueryParams): Promise<PageResult<UserRecord>> {
    const response = await request.get<PageResult<UserRecord>>(
      USER_API.PAGE, // 使用常量，不是硬编码
      params
    );
    return response.data;
  }
}
```

---

## 5. TypeScript类型安全要求

### 🔒 强制要求

#### 5.1 常量类型推断
使用 `as const` 确保类型推断：

```typescript
export const USER_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
} as const;

export type UserStatusValue = typeof USER_STATUS[keyof typeof USER_STATUS];
```

#### 5.2 枚举替代方案
使用const对象替代enum：

```typescript
// ✅ 推荐
export const UserGender = {
  MALE: 1,
  FEMALE: 2,
  UNKNOWN: 0,
} as const;

// ❌ 避免使用
export enum UserGender {
  MALE = 1,
  FEMALE = 2,
  UNKNOWN = 0,
}
```

---

## 6. 代码检查清单

### 📝 每次开发必须验证

在提交代码前，必须逐项检查：

- [ ] **API路径检查**：是否使用了API常量而非硬编码路径？
- [ ] **类型重复检查**：是否存在重复的类型定义？
- [ ] **导入规范检查**：类型是否从统一的types模块导入？
- [ ] **模块化检查**：是否遵循了模块化分离原则？
- [ ] **TypeScript检查**：TypeScript类型检查是否通过？
- [ ] **命名规范检查**：API方法命名是否遵循RESTful规范？
- [ ] **文档更新检查**：是否更新了相关文档？

---

## 7. 示例对比

### ❌ 违规代码示例

```typescript
// 错误：硬编码路径 + 重复类型定义
import request from '@/utils/request';

interface UserQueryParams {  // 重复定义
  username?: string;
}

export class UserApi {
  static async getUsers(params: UserQueryParams) {
    return await request.get('/users/page', params); // 硬编码
  }
  
  static async updateUser(id: number, data: any) {
    return await request.put(`/users/${id}`, data); // 硬编码
  }
}
```

### ✅ 规范代码示例

```typescript
// 正确：使用常量 + 导入类型
import request from '@/utils/request';
import { USER_API } from '@/constants/api';
import type { 
  PageResult,
  UserRecord,
  UserQueryParams,
  UserFormData
} from '@/types';

export class UserApi {
  static async getUserPage(params: UserQueryParams): Promise<PageResult<UserRecord>> {
    const response = await request.get<PageResult<UserRecord>>(
      USER_API.PAGE, // 使用常量
      params
    );
    return response.data;
  }
  
  static async updateUser(id: number, userData: Partial<UserFormData>): Promise<UserRecord> {
    const response = await request.put<UserRecord>(
      USER_API.UPDATE(id), // 使用常量函数
      userData
    );
    return response.data;
  }
}
```

---

## 🚨 违规处理

**发现违反以上规范的代码必须立即重构，不得合并到主分支。**

### 处理流程：
1. **代码审查阶段**：严格检查是否符合规范
2. **违规发现**：立即要求修改，提供具体修改建议
3. **重构完成**：重新审查确认符合规范
4. **文档更新**：更新相关技术文档

---

## 📚 相关文档

- [API常量定义文档](./api-constants.md)
- [类型定义规范文档](./type-definitions.md)
- [项目架构说明](./architecture.md)

---

**版本信息**：v2.0 | **更新时间**：2025-06-27 | **状态**：强制执行
