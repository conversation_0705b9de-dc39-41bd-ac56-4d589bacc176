# 存储功能完善文档

## 🎯 **改进目标**

根据用户要求，完善登录信息的存储管理：
1. **存储到LocalStorage** - 登录信息需要持久化存储
2. **优先从缓存获取** - 获取时优先从浏览器缓存获取
3. **定义存储常量** - 减少硬编码，统一管理存储Key

## ✅ **已完成的改进**

### 1. **创建存储常量定义**
**文件**: `src/constants/storage.ts`

- **认证相关存储Key**: 访问令牌、刷新令牌、用户信息、菜单权限等
- **用户偏好设置Key**: 主题、语言、用户偏好等
- **应用设置Key**: 应用版本、访问时间、路由缓存等
- **业务数据缓存Key**: 客户列表、用户列表、角色列表等

```typescript
export const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'combo_opm_access_token',
  REFRESH_TOKEN: 'combo_opm_refresh_token',
  USER_INFO: 'combo_opm_user_info',
  USER_MENUS: 'combo_opm_user_menus',
  USER_PERMISSIONS: 'combo_opm_user_permissions',
  USER_ROLES: 'combo_opm_user_roles',
  LOGIN_STATUS: 'combo_opm_login_status',
  TOKEN_EXPIRES_AT: 'combo_opm_token_expires_at',
} as const;
```

### 2. **创建存储工具类**
**文件**: `src/utils/storage.ts`

#### **StorageManager类**
- 统一的localStorage操作接口
- 支持数据过期时间设置
- 自动JSON序列化/反序列化
- 错误处理和容错机制
- 存储使用情况监控

#### **AuthStorageManager类**
- 专门的认证数据存储管理
- Token管理（访问令牌、刷新令牌）
- 用户信息存储
- 权限和角色数据管理
- 一键清除认证数据

```typescript
// 设置访问令牌（带过期时间）
AuthStorageManager.setAccessToken(token, expires);

// 获取访问令牌
const token = AuthStorageManager.getAccessToken();

// 设置用户信息
AuthStorageManager.setUserInfo(userInfo);

// 清除所有认证数据
AuthStorageManager.clearAuthData();
```

### 3. **更新认证状态管理**
**文件**: `src/stores/modules/auth.ts`

#### **优化的数据存储流程**
1. **登录成功时**: 同时保存到Pinia状态和localStorage
2. **应用启动时**: 优先从localStorage恢复状态
3. **退出登录时**: 清除所有存储数据

#### **改进的initAuth方法**
```typescript
const initAuth = async (): Promise<void> => {
  // 优先从localStorage获取缓存的认证信息
  const cachedToken = AuthStorageManager.getAccessToken();
  const cachedUserInfo = AuthStorageManager.getUserInfo<UserLoginRecord>();
  const cachedMenus = AuthStorageManager.getUserMenus<MenuRecord[]>();
  
  if (cachedToken && cachedUserInfo) {
    // 恢复状态
    accessToken.value = cachedToken;
    userInfo.value = cachedUserInfo;
    userMenus.value = cachedMenus || [];
    loginStatus.value = LoginStatus.LOGGED_IN;
  }
};
```

### 4. **更新用户偏好管理**
**文件**: `src/stores/modules/user.ts`

- 使用StorageManager管理用户偏好设置
- 支持偏好设置的持久化存储
- 应用启动时自动加载用户偏好

### 5. **更新请求工具**
**文件**: `src/utils/request.ts`

- TokenManager现在使用AuthStorageManager
- 保持向后兼容性
- 统一的Token管理接口

## 🔧 **技术特性**

### **存储数据结构**
```typescript
interface StorageData<T = any> {
  value: T;                // 实际存储的值
  expires?: number;        // 过期时间戳，-1表示永不过期
  timestamp: number;       // 存储时间戳
}
```

### **过期时间管理**
- **Token默认过期时间**: 7天
- **缓存数据过期时间**: 1小时
- **用户偏好**: 永不过期
- **自动过期检查**: 获取数据时自动检查并清理过期数据

### **错误处理**
- localStorage不可用时的降级处理
- JSON解析错误的容错机制
- 详细的错误日志记录

## 🧪 **测试功能**

### **存储测试页面**
**文件**: `src/views/StorageTest.vue`
**访问路径**: http://localhost:3001/storage-test

测试功能包括：
1. **Token存储测试** - 保存、读取、清除Token
2. **用户信息存储测试** - 用户数据的存储和读取
3. **偏好设置测试** - 主题、语言等偏好设置
4. **存储信息查看** - 查看localStorage使用情况
5. **数据清除功能** - 清除认证数据或所有数据

## 📊 **存储Key命名规范**

### **前缀规则**
所有存储Key都以 `combo_opm_` 为前缀，避免与其他应用冲突

### **分类管理**
- `combo_opm_access_token` - 访问令牌
- `combo_opm_user_info` - 用户信息
- `combo_opm_user_preferences` - 用户偏好
- `combo_opm_cache_*` - 缓存数据

## 🔄 **数据流程**

### **登录流程**
1. 用户登录 → API调用
2. 登录成功 → 保存到Pinia状态
3. 同时保存 → localStorage（使用AuthStorageManager）
4. 设置过期时间 → 7天后自动清理

### **应用启动流程**
1. 应用启动 → 调用initAuth()
2. 检查localStorage → 优先使用缓存数据
3. 恢复Pinia状态 → 无需重新登录
4. 验证Token有效性 → 可选的安全检查

### **退出登录流程**
1. 用户退出 → 调用logout()
2. 清除Pinia状态 → 重置所有状态
3. 清除localStorage → 使用clearAuthData()
4. 跳转登录页 → 完成退出流程

## 🎯 **优势总结**

1. **✅ 避免硬编码** - 所有存储Key统一管理
2. **✅ 数据持久化** - 页面刷新后保持登录状态
3. **✅ 优先缓存读取** - 提升应用启动速度
4. **✅ 自动过期管理** - 安全的数据生命周期
5. **✅ 类型安全** - 完整的TypeScript支持
6. **✅ 错误容错** - 健壮的错误处理机制
7. **✅ 易于维护** - 清晰的代码结构和文档

## 🔮 **后续优化建议**

1. **加密存储** - 敏感数据可考虑加密存储
2. **存储配额管理** - 监控和管理localStorage使用量
3. **数据同步** - 多标签页之间的数据同步
4. **备份恢复** - 重要数据的备份和恢复机制

---

**✨ 存储功能已完全按照用户要求进行了优化，实现了LocalStorage持久化存储、优先缓存读取和统一的常量管理！**
