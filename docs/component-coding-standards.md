# 🎯 Vue 3 + TypeScript + Ant Design Vue 组件封装编码规范 v3.0

> **重要声明**: 此规范为项目强制执行标准，所有组件开发必须严格遵循！

## 📋 核心原则

### 🚨 强制要求
1. **字符串常量化**: 所有业务相关字符串必须定义为常量，禁止硬编码
2. **类型安全**: 所有Props、Emits必须有完整TypeScript类型定义
3. **注释完整**: 每个组件、方法、属性都必须有详细注释
4. **命名一致**: 严格遵循项目命名规范
5. **样式隔离**: 必须使用scoped样式，使用CSS变量

### ❌ 禁止事项
1. **盲目常量化**: 禁止将Vue约定、HTML标准、CSS标准值常量化
2. **业务硬编码**: 禁止业务相关的硬编码字符串、数字、颜色
3. **重复定义**: 禁止在多个地方定义相同的类型或常量
4. **样式污染**: 禁止不使用scoped的全局样式
5. **缺少注释**: 禁止没有注释的代码

## 📁 1. 文件结构规范

### 1.1 目录结构
```
src/components/
├── base/           # 基础UI组件(Button、Input、Card等)
├── business/       # 业务组件(UserCard、OrderTable、ProductList等)
├── layout/         # 布局组件(Header、Sidebar、Footer等)
├── common/         # 通用功能组件(Loading、Modal、Pagination等)
└── form/           # 表单组件(FormItem、FormGroup等)
```

### 1.2 组件文件结构
```
ComponentName/
├── index.ts          # 导出文件
├── ComponentName.vue # 主组件文件
├── types.ts          # 组件特有类型定义
└── __tests__/        # 测试文件(可选)
    └── ComponentName.test.ts
```

## 🏷️ 2. 命名规范

- **组件文件名**: `PascalCase` (`UserInfoCard.vue`)
- **组件目录名**: `PascalCase` (`UserInfoCard/`)
- **组件注册名**: `PascalCase` (`<UserInfoCard />`)
- **业务组件前缀**: 项目名 (`ComboUserCard`)
- **Props接口**: `组件名Props` (`UserCardProps`)
- **Emits接口**: `组件名Emits` (`UserCardEmits`)

## 📝 3. 组件结构规范

### 3.1 标准结构顺序
```vue
<template>
  <!-- 
    模板内容
    注释说明：描述模板的主要结构和功能
  -->
</template>

<script setup lang="ts">
/**
 * 组件功能描述
 * @description 详细描述组件的用途和功能
 * <AUTHOR>
 * @version 1.0.0
 * @example
 * <ComponentName 
 *   :prop1="value1"
 *   @event1="handler1"
 * />
 */

// 1. 导入依赖 (Vue API、第三方库、项目内部)
// 2. 导入类型定义
// 3. 导入常量 (重要：必须使用项目统一常量)
// 4. Props定义 (包含完整注释)
// 5. Emits定义 (包含完整注释)
// 6. 组合函数调用
// 7. 响应式数据定义 (包含注释说明用途)
// 8. 计算属性 (包含注释说明计算逻辑)
// 9. 方法定义 (包含完整注释)
// 10. 生命周期钩子 (包含注释说明执行时机)
// 11. 组件导出(defineOptions)
</script>

<style scoped>
/* 
  样式定义
  注释说明：描述样式的用途和设计意图
*/
</style>
```

### 3.2 导入顺序规范
```typescript
// 1. Vue 相关导入
import { ref, computed, onMounted } from 'vue';

// 2. 第三方库导入
import { message } from 'ant-design-vue';

// 3. 项目内部 - 类型导入
import type { UserInfo, ActionEvent } from './types';
import type { ComponentSize, ComponentTheme } from '@/types/components';

// 4. 项目内部 - 常量导入 (重要：必须使用统一常量)
import { 
  COMPONENT_SIZES, 
  COMPONENT_THEMES, 
  ACTION_TYPES,
  STATUS_TYPES,
  FORM_MESSAGES 
} from '@/constants';

// 5. 项目内部 - 工具函数导入
import { formatDate, validateEmail } from '@/utils';
```

## 🎛️ 4. Props和Emits规范

### 4.1 Props定义规范
```typescript
// 简单组件 - 直接定义
interface Props {
  /** 组件标题 */
  title: string;
  
  /** 组件尺寸，支持小、中、大三种规格 */
  size?: ComponentSize;
  
  /** 是否禁用组件 */
  disabled?: boolean;
}

// 复杂组件 - 对象化分组
interface Props {
  /** 用户信息对象 */
  userInfo: UserInfo;
  
  /** 卡片配置选项，支持部分配置 */
  config?: Partial<CardConfig>;
  
  /** 操作按钮列表 */
  actions?: ActionItem[];
}

// 默认值定义 - 必须使用常量
const props = withDefaults(defineProps<Props>(), {
  size: COMPONENT_SIZES.MEDIUM,     // ✅ 使用常量
  disabled: false,
  theme: COMPONENT_THEMES.DEFAULT,  // ✅ 使用常量
  config: () => ({
    size: COMPONENT_SIZES.MEDIUM,
    showActions: true,
    theme: COMPONENT_THEMES.DEFAULT  // ✅ 使用常量
  })
});
```

### 4.2 Emits定义规范
```typescript
interface Emits {
  /** 统一的操作事件，所有用户操作都通过此事件触发 */
  action: [event: ActionEvent];
  
  /** Vue v-model 双向绑定事件 - 保持Vue约定，不使用常量 */
  'update:modelValue': [value: any];
  
  /** 可见性双向绑定事件 - 保持Vue约定，不使用常量 */
  'update:visible': [visible: boolean];
}
```

## 🔤 5. 字符串常量管理规范 (核心重点)

### 5.1 常量定义位置
```
src/constants/
├── components/
│   ├── index.ts      # 组件常量统一导出
│   ├── sizes.ts      # 尺寸相关常量
│   ├── themes.ts     # 主题相关常量
│   ├── actions.ts    # 操作类型常量
│   ├── status.ts     # 状态类型常量
│   └── messages.ts   # 消息文本常量
```

### 5.2 常量定义示例
```typescript
// src/constants/components/index.ts
export const COMPONENT_CONSTANTS = {
  // 组件尺寸常量
  SIZES: {
    MINI: 'mini',
    SMALL: 'small', 
    MEDIUM: 'medium',
    LARGE: 'large'
  },
  
  // 操作类型常量 - 重要：所有操作都必须使用这些常量
  ACTIONS: {
    CREATE: 'create',
    UPDATE: 'update',
    DELETE: 'delete',
    EDIT: 'edit',
    VIEW: 'view',
    COPY: 'copy'
  },
  
  // 状态类型常量
  STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    PENDING: 'pending',
    COMPLETED: 'completed'
  }
} as const;

// 消息文本常量 - 重要：所有用户可见的文本都应该定义
export const COMPONENT_MESSAGES = {
  SUCCESS: {
    CREATE: '创建成功',
    UPDATE: '更新成功',
    DELETE: '删除成功'
  },
  CONFIRM: {
    DELETE: '确定要删除这条记录吗？',
    RESET: '确定要重置表单吗？'
  },
  PLACEHOLDERS: {
    USERNAME: '请输入用户名',
    EMAIL: '请输入邮箱地址'
  }
} as const;

// 类型导出 - 重要：必须导出对应的TypeScript类型
export type ComponentSize = typeof COMPONENT_CONSTANTS.SIZES[keyof typeof COMPONENT_CONSTANTS.SIZES];
export type ActionType = typeof COMPONENT_CONSTANTS.ACTIONS[keyof typeof COMPONENT_CONSTANTS.ACTIONS];
export type StatusType = typeof COMPONENT_CONSTANTS.STATUS[keyof typeof COMPONENT_CONSTANTS.STATUS];
```

### 5.3 字符串常量化判断标准 (重要规则)

#### ✅ **必须常量化的字符串**
```typescript
// 1. 业务操作类型
const handleEdit = () => {
  emit('action', {
    type: COMPONENT_CONSTANTS.ACTIONS.EDIT,  // ✅ 正确：使用常量
    payload: userInfo
  });
};

// 2. 组件配置值
const defaultConfig = {
  size: COMPONENT_CONSTANTS.SIZES.MEDIUM,     // ✅ 正确：使用常量
  theme: COMPONENT_CONSTANTS.THEMES.DEFAULT   // ✅ 正确：使用常量
};

// 3. 用户可见文本
message.success(COMPONENT_MESSAGES.SUCCESS.CREATE);   // ✅ 正确
```

#### ❌ **不应常量化的字符串**
```typescript
// 1. Vue框架约定 - 保持原样
interface Emits {
  'update:modelValue': [value: any];     // ❌ 不要常量化：Vue约定
  'update:visible': [visible: boolean];  // ❌ 不要常量化：Vue约定
}

// 2. HTML标准属性 - 保持原样
const buttonProps = {
  type: 'button',    // ❌ 不要常量化：HTML标准
  target: '_blank'   // ❌ 不要常量化：HTML标准
};

// 3. CSS标准值 - 保持原样
const styles = {
  display: 'flex',      // ❌ 不要常量化：CSS标准
  position: 'absolute'  // ❌ 不要常量化：CSS标准
};
```

### 5.4 常量使用检查清单 (必须遵守)

遇到字符串时，必须问自己：

1. **是否是业务逻辑相关？** → 是 = 必须常量化
2. **是否可能在多个组件中使用？** → 是 = 必须常量化  
3. **是否可能需要统一修改？** → 是 = 必须常量化
4. **是否是Vue/HTML/CSS标准约定？** → 是 = 保持原样
5. **是否是用户可见的文本？** → 是 = 必须常量化

## 📖 6. 注释规范 (强制要求)

### 6.1 组件级注释
```typescript
/**
 * 用户信息卡片组件
 * @description 展示用户基本信息的卡片组件，支持多种尺寸、主题和操作
 * 主要功能：
 * 1. 显示用户头像、姓名、邮箱等基本信息
 * 2. 支持自定义操作按钮（编辑、删除、查看等）
 * 3. 支持多种尺寸和主题样式
 * 4. 响应式设计，适配移动端
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 * 
 * @example
 * <UserInfoCard 
 *   :user-info="userInfo" 
 *   :config="{ size: 'medium', showActions: true }"
 *   :actions="userActions"
 *   @action="handleUserAction" 
 * />
 */
```

### 6.2 Props注释规范
```typescript
interface Props {
  /**
   * 用户信息对象
   * @description 包含用户的基本信息，如姓名、邮箱、头像等
   * @required 必填字段
   */
  userInfo: UserInfo;
  
  /**
   * 卡片配置选项
   * @description 控制卡片的外观和行为
   * @default { size: 'medium', showActions: true, theme: 'default' }
   * @optional 可选字段，使用默认配置
   */
  config?: Partial<CardConfig>;
}
```

### 6.3 方法注释规范
```typescript
/**
 * 处理用户操作事件
 * @description 统一处理所有用户操作，包括编辑、删除、查看等
 * @param action 操作项对象，包含操作类型和相关信息
 * @returns void
 * @emits action 向父组件发送操作事件
 * 
 * @example
 * handleAction({ key: 'edit', label: '编辑' })
 */
const handleAction = (action: ActionItem): void => {
  // 方法实现...
};
```

## 🎨 7. 样式规范

### 7.1 样式结构
```vue
<style scoped>
/* 
  组件样式说明
  设计说明：采用卡片式设计，支持多种尺寸和主题
*/

/* 1. 组件根样式 - 基础布局和外观 */
.combo-user-card {
  /* 使用CSS变量而不是硬编码值 */
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
}

/* 2. 尺寸变体 */
.combo-user-card--small {
  padding: var(--spacing-sm);
}

/* 3. 主题变体 */
.combo-user-card--primary {
  border-color: var(--color-primary);
}

/* 4. 响应式样式 */
@media (max-width: 768px) {
  .combo-user-card {
    padding: var(--spacing-sm);
  }
}
</style>
```

### 7.2 样式规范要求
- ✅ 必须使用 `scoped` 避免样式污染
- ✅ 使用CSS变量支持主题切换，禁止硬编码颜色值
- ✅ 遵循BEM命名规范：`.combo-组件名__元素--修饰符`
- ✅ 每个样式块必须有注释说明用途

## 📤 8. 组件导出规范

### 8.1 组件导出
```typescript
// ComponentName.vue
defineOptions({
  name: 'ComboUserCard',        // 组件名称
  inheritAttrs: false           // 不自动继承父组件的属性
});
```

### 8.2 统一导出
```typescript
// ComponentName/index.ts
export { default as UserInfoCard } from './UserInfoCard.vue';
export type { 
  UserCardProps, 
  UserCardEmits,
  UserInfo,
  ActionItem 
} from './types';
```

## 🧪 9. 组件测试规范

```typescript
// __tests__/UserInfoCard.test.ts
import { mount } from '@vue/test-utils';
import UserInfoCard from '../UserInfoCard.vue';
import { COMPONENT_SIZES } from '@/constants';

describe('UserInfoCard', () => {
  it('should render correctly with constants', () => {
    const wrapper = mount(UserInfoCard, {
      props: {
        userInfo: mockUserInfo,
        config: {
          size: COMPONENT_SIZES.MEDIUM  // ✅ 测试中也使用常量
        }
      }
    });
    
    expect(wrapper.find('.combo-user-card').exists()).toBe(true);
  });
});
```

## 📋 10. 检查清单

在提交组件代码前，必须检查：

### ✅ 代码质量检查
- [ ] 所有业务字符串都使用了常量定义
- [ ] 没有硬编码的颜色、尺寸、文本
- [ ] 所有Props和Emits都有完整的TypeScript类型
- [ ] 每个组件、方法、属性都有详细注释
- [ ] 样式使用了scoped和CSS变量
- [ ] 遵循了BEM命名规范

### ✅ 功能完整性检查
- [ ] 组件支持所需的尺寸和主题
- [ ] 响应式设计正常工作
- [ ] 事件处理正确实现
- [ ] 默认值使用了常量
- [ ] 错误处理完善

### ✅ 文档完整性检查
- [ ] 组件有完整的使用示例
- [ ] Props和Emits都有详细说明
- [ ] 复杂逻辑有注释解释
- [ ] 导出了所有必要的类型

## 🎯 11. 完整组件示例

### 11.1 标准组件实现
```vue
<!-- UserInfoCard.vue -->
<template>
  <!--
    用户信息卡片模板
    功能：展示用户基本信息和操作按钮
  -->
  <div :class="cardClasses">
    <!-- 用户信息头部 -->
    <div class="combo-user-card__header">
      <img
        :src="userInfo.avatar"
        :alt="userInfo.name"
        class="combo-user-card__avatar"
      />
      <div class="combo-user-card__info">
        <h3 class="combo-user-card__name">{{ userInfo.name }}</h3>
        <p class="combo-user-card__email">{{ userInfo.email }}</p>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div v-if="config.showActions" class="combo-user-card__actions">
      <a-button
        v-for="action in actions"
        :key="action.key"
        :size="config.size"
        :disabled="action.disabled"
        @click="handleAction(action)"
      >
        {{ action.label }}
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 用户信息卡片组件
 * @description 展示用户基本信息的卡片组件，支持多种尺寸、主题和操作
 * 主要功能：
 * 1. 显示用户头像、姓名、邮箱等基本信息
 * 2. 支持自定义操作按钮（编辑、删除、查看等）
 * 3. 支持多种尺寸和主题样式
 * 4. 响应式设计，适配移动端
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 *
 * @example
 * <UserInfoCard
 *   :user-info="userInfo"
 *   :config="{ size: 'medium', showActions: true }"
 *   :actions="userActions"
 *   @action="handleUserAction"
 * />
 */

// 1. Vue相关导入
import { computed } from 'vue';

// 2. 第三方库导入
import { message } from 'ant-design-vue';

// 3. 类型导入
import type { UserInfo, ActionItem, ActionEvent, CardConfig } from './types';

// 4. 常量导入 - 重要：必须使用统一常量
import {
  COMPONENT_CONSTANTS,
  COMPONENT_MESSAGES
} from '@/constants';

// 5. Props定义
interface Props {
  /**
   * 用户信息对象
   * @description 包含用户的基本信息，如姓名、邮箱、头像等
   * @required 必填字段
   */
  userInfo: UserInfo;

  /**
   * 卡片配置选项
   * @description 控制卡片的外观和行为
   * @default { size: 'medium', showActions: true, theme: 'default' }
   * @optional 可选字段，使用默认配置
   */
  config?: Partial<CardConfig>;

  /**
   * 操作按钮列表
   * @description 定义卡片中显示的操作按钮
   * @optional 可选字段，不传则不显示操作区域
   */
  actions?: ActionItem[];
}

// 6. 默认值定义 - 使用常量
const props = withDefaults(defineProps<Props>(), {
  config: () => ({
    size: COMPONENT_CONSTANTS.SIZES.MEDIUM,
    showActions: true,
    theme: COMPONENT_CONSTANTS.THEMES.DEFAULT,
    bordered: true
  })
});

// 7. Emits定义
interface Emits {
  /**
   * 统一的操作事件
   * @description 所有用户操作都通过此事件触发
   */
  action: [event: ActionEvent];
}

const emit = defineEmits<Emits>();

// 8. 计算属性
/**
 * 计算卡片的CSS类名
 * @description 根据配置生成对应的CSS类名数组
 * @returns string[] CSS类名数组
 */
const cardClasses = computed((): string[] => {
  return [
    'combo-user-card',                                    // 基础类名
    `combo-user-card--${props.config?.size}`,           // 尺寸类名
    `combo-user-card--${props.config?.theme}`,          // 主题类名
    props.config?.bordered && 'combo-user-card--bordered' // 边框类名
  ].filter(Boolean);
});

// 9. 方法定义
/**
 * 处理用户操作事件
 * @description 统一处理所有用户操作，包括编辑、删除、查看等
 * @param action 操作项对象，包含操作类型和相关信息
 * @returns void
 * @emits action 向父组件发送操作事件
 */
const handleAction = (action: ActionItem): void => {
  // 验证操作权限
  if (action.permission && !hasPermission(action.permission)) {
    message.warning(COMPONENT_MESSAGES.ERROR.PERMISSION_DENIED);
    return;
  }

  // 构造事件对象，使用预定义常量
  const actionEvent: ActionEvent = {
    type: action.key,           // 使用常量定义的操作类型
    payload: props.userInfo,    // 传递用户信息
    target: 'user-card'         // 标识事件来源
  };

  // 发送事件给父组件
  emit('action', actionEvent);
};

// 10. 组件选项定义
defineOptions({
  name: 'ComboUserCard',
  inheritAttrs: false
});
</script>

<style scoped>
/*
  用户信息卡片组件样式
  设计说明：采用卡片式设计，支持多种尺寸和主题
*/

/* 1. 组件根样式 - 基础布局和外观 */
.combo-user-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  background: var(--bg-color);
  transition: all var(--transition-duration) var(--transition-timing);
}

/* 2. 尺寸变体 */
.combo-user-card--small {
  padding: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.combo-user-card--medium {
  padding: var(--spacing-md);
  font-size: var(--font-size-base);
}

.combo-user-card--large {
  padding: var(--spacing-lg);
  font-size: var(--font-size-lg);
}

/* 3. 主题变体 */
.combo-user-card--primary {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

/* 4. 子元素样式 */
.combo-user-card__header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.combo-user-card__avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.combo-user-card__info {
  flex: 1;
  min-width: 0;
}

.combo-user-card__name {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.combo-user-card__email {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}

.combo-user-card__actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-xs);
}

/* 5. 响应式样式 */
@media (max-width: 768px) {
  .combo-user-card {
    padding: var(--spacing-sm);
  }

  .combo-user-card__header {
    flex-direction: column;
    text-align: center;
  }

  .combo-user-card__actions {
    flex-direction: column;
  }
}
</style>
```

### 11.2 类型定义文件
```typescript
// types.ts
import type { ComponentSize, ComponentTheme } from '@/types/components';
import type { ActionType } from '@/constants';

/**
 * 用户信息接口
 * @description 定义用户的基本信息结构
 */
export interface UserInfo {
  /** 用户唯一标识 */
  id: string;
  /** 用户姓名 */
  name: string;
  /** 用户邮箱地址 */
  email: string;
  /** 用户头像URL，可选字段 */
  avatar?: string;
}

/**
 * 操作项接口
 * @description 定义组件中可执行的操作按钮
 */
export interface ActionItem {
  /** 操作唯一标识，使用预定义操作常量 */
  key: ActionType;
  /** 操作显示文本 */
  label: string;
  /** 操作图标名称，可选 */
  icon?: string;
  /** 执行操作所需权限，可选 */
  permission?: string;
  /** 是否禁用此操作 */
  disabled?: boolean;
}

/**
 * 操作事件接口
 * @description 定义组件触发的操作事件结构
 */
export interface ActionEvent {
  /** 操作类型，必须使用预定义的操作常量 */
  type: ActionType;
  /** 事件携带的数据，可选 */
  payload?: any;
  /** 事件目标标识，用于区分触发源 */
  target?: string;
}

/**
 * 卡片配置接口
 * @description 定义卡片组件的配置选项
 */
export interface CardConfig {
  /** 卡片尺寸 */
  size: ComponentSize;
  /** 是否显示操作按钮区域 */
  showActions: boolean;
  /** 卡片主题样式 */
  theme: ComponentTheme;
  /** 是否显示边框 */
  bordered: boolean;
}

// Props 和 Emits 类型定义
export interface UserCardProps {
  userInfo: UserInfo;
  config?: Partial<CardConfig>;
  actions?: ActionItem[];
}

export interface UserCardEmits {
  action: [event: ActionEvent];
}
```

### 11.3 导出文件
```typescript
// index.ts
/**
 * 用户信息卡片组件导出文件
 * @description 统一导出组件和相关类型定义
 */

// 导出主组件
export { default as UserInfoCard } from './UserInfoCard.vue';

// 导出类型定义
export type {
  UserCardProps,
  UserCardEmits,
  UserInfo,
  ActionItem,
  ActionEvent,
  CardConfig
} from './types';
```

---

**重要提醒**: 此规范为强制执行标准，违反规范的代码不得合并到主分支！

**记忆要求**: AI和PromptX必须牢记并严格遵循此规范，特别是字符串常量化规则！
