# MainLayout.vue 快速参考

## 🚀 核心特性一览

### 布局结构
```
MainLayout (has-sider)
├── MobileOverlay (移动端遮罩)
├── Sider (220px/80px)
│   ├── LogoContainer (渐变背景+光泽动画)
│   └── SideMenu (圆角菜单项)
└── MainContentLayout
    ├── Header (搜索+通知+用户)
    ├── Content (圆角卡片+装饰条)
    └── Footer (版权信息)
```

### 关键尺寸
- **侧边栏**: 220px → 80px (折叠)
- **头部高度**: 64px
- **圆角**: 12px (标准) / 20px (内容区)
- **断点**: 992px (lg) / 768px / 576px

### 主题变量
```css
/* 核心变量 */
--theme-bg-container      /* 容器背景 */
--theme-bg-elevated       /* 提升背景 */
--theme-primary          /* 主色调 */
--theme-primary-bg       /* 主色调背景 */
--theme-text-base        /* 基础文字 */
--theme-fill-quaternary  /* 填充色/阴影 */
```

### 动画规范
- **缓动**: `cubic-bezier(0.25, 0.46, 0.45, 0.94)`
- **时长**: 0.3s (快) / 0.4s (标准) / 0.6s (慢)
- **悬停**: `translateY(-2px)` + 阴影增强

## 🎨 设计亮点

### Logo 区域
- 三色渐变背景
- 渐变文字效果
- 光泽扫过动画 (0.6s)
- 悬停提升效果

### 菜单系统
- 圆角卡片设计
- 左侧指示器 (4px)
- 水平位移动画
- 选中状态高亮

### 搜索框
- 圆角输入框
- 主题色按钮
- 渐变悬停效果
- 移动端隐藏

### 内容区域
- 圆角卡片容器
- 顶部装饰条 (渐变)
- 悬停提升效果
- 路由过渡动画

## 📱 响应式行为

### 桌面端 (≥992px)
- 固定侧边栏
- 完整功能显示
- 内容区左边距 220px

### 平板端 (768px-991px)
- 可折叠侧边栏
- 保持主要功能
- 动态边距调整

### 移动端 (≤767px)
- 抽屉式侧边栏
- 遮罩层覆盖
- 搜索框隐藏
- 面包屑隐藏 (≤576px)
- 用户名隐藏 (≤768px)

## 🔧 状态管理

### 响应式状态
```typescript
const collapsed = ref(false)           // 侧边栏折叠
const selectedKeys = ref(["dashboard"]) // 选中菜单
const searchText = ref("")             // 搜索文本
const isMobile = ref(false)            // 移动端状态
const mobileMenuVisible = ref(false)   // 移动菜单显示
```

### 核心方法
- `toggleCollapsed()` - 切换侧边栏
- `toggleTheme()` - 切换主题
- `handleSearch()` - 处理搜索
- `onBreakpoint()` - 断点处理

## 🎯 使用要点

### 样式修改
1. 优先使用主题变量
2. 保持动画一致性
3. 注意响应式兼容

### 功能扩展
1. 菜单项需更新路由
2. 主题变量在 ThemeProvider 定义
3. 响应式需考虑所有断点

### 性能优化
1. 使用 GPU 加速动画
2. 条件渲染移动端组件
3. 防抖搜索输入

## 📋 检查清单

### 设计一致性
- [ ] 使用统一的圆角半径
- [ ] 使用统一的间距系统
- [ ] 使用统一的动画缓动
- [ ] 使用主题变量而非硬编码

### 响应式适配
- [ ] 测试所有断点
- [ ] 验证移动端交互
- [ ] 检查内容溢出
- [ ] 确认触摸友好

### 主题兼容
- [ ] 默认主题正常
- [ ] 暗色主题正常
- [ ] 自定义主题正常
- [ ] 切换过渡流畅

### 性能表现
- [ ] 动画流畅 60fps
- [ ] 无内存泄漏
- [ ] 快速响应交互
- [ ] 合理的包大小

---

*快速参考 v1.0.0 | 更新于 2025-06-27*
