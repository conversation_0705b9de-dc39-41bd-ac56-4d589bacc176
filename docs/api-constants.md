# API常量定义规范文档

> 本文档详细说明API常量的定义规范和使用方法

## 📋 目录

- [1. 常量文件结构](#1-常量文件结构)
- [2. 命名规范](#2-命名规范)
- [3. 动态路径处理](#3-动态路径处理)
- [4. 使用示例](#4-使用示例)
- [5. 最佳实践](#5-最佳实践)

---

## 1. 常量文件结构

### 文件位置
```
src/constants/api.ts
```

### 基本结构
```typescript
/**
 * API接口地址常量定义
 * 所有API路径必须在此文件中定义，严禁硬编码
 */

/**
 * 认证相关接口
 */
export const AUTH_API = {
  /** 用户登录 */
  LOGIN: '/auth/login',
  /** 用户登出 */
  LOGOUT: '/auth/logout',
  /** 刷新token */
  REFRESH_TOKEN: '/auth/refresh',
  /** 获取当前用户信息 */
  CURRENT_USER: '/auth/user',
  /** 获取用户菜单权限 */
  USER_MENUS: '/auth/menus',
  /** 修改密码 */
  CHANGE_PASSWORD: '/auth/change-password',
  /** 验证token有效性 */
  VALIDATE_TOKEN: '/auth/validate',
} as const;

/**
 * 用户管理接口
 */
export const USER_API = {
  /** 获取用户列表 */
  LIST: '/users',
  /** 分页查询用户 */
  PAGE: '/users/page',
  /** 获取用户详情 */
  DETAIL: (id: number) => `/users/${id}`,
  /** 创建用户 */
  CREATE: '/users',
  /** 更新用户 */
  UPDATE: (id: number) => `/users/${id}`,
  /** 删除用户 */
  DELETE: (id: number) => `/users/${id}`,
  /** 批量删除用户 */
  BATCH_DELETE: '/users/batch-delete',
  /** 重置密码 */
  RESET_PASSWORD: (id: number) => `/users/${id}/reset-password`,
  /** 切换用户状态 */
  TOGGLE_STATUS: (id: number) => `/users/${id}/status`,
} as const;

/**
 * 导出所有API常量
 */
export const API_ENDPOINTS = {
  AUTH: AUTH_API,
  USER: USER_API,
  CUSTOMER: CUSTOMER_API,
  ROLE: ROLE_API,
  MENU: MENU_API,
  COMMON: COMMON_API,
} as const;
```

---

## 2. 命名规范

### 2.1 模块命名
- 使用 `{MODULE}_API` 格式
- 模块名使用大写单词，如：`USER_API`、`CUSTOMER_API`

### 2.2 接口命名
- 使用大写单词，下划线分隔
- 遵循RESTful规范：

| 操作类型 | 命名规范 | 示例 |
|---------|---------|------|
| 列表查询 | `LIST` | `USER_API.LIST` |
| 分页查询 | `PAGE` | `USER_API.PAGE` |
| 详情查询 | `DETAIL` | `USER_API.DETAIL(id)` |
| 创建 | `CREATE` | `USER_API.CREATE` |
| 更新 | `UPDATE` | `USER_API.UPDATE(id)` |
| 删除 | `DELETE` | `USER_API.DELETE(id)` |
| 批量操作 | `BATCH_{ACTION}` | `USER_API.BATCH_DELETE` |
| 状态切换 | `TOGGLE_{FIELD}` | `USER_API.TOGGLE_STATUS(id)` |

### 2.3 注释规范
每个接口必须添加JSDoc注释：

```typescript
export const USER_API = {
  /** 获取用户列表 */
  LIST: '/users',
  /** 分页查询用户 */
  PAGE: '/users/page',
  /** 获取用户详情 */
  DETAIL: (id: number) => `/users/${id}`,
} as const;
```

---

## 3. 动态路径处理

### 3.1 单参数路径
```typescript
// ✅ 正确
DETAIL: (id: number) => `/users/${id}`,
UPDATE: (id: number) => `/users/${id}`,
DELETE: (id: number) => `/users/${id}`,

// ❌ 错误
DETAIL: '/users/:id',
UPDATE: '/users/{id}',
```

### 3.2 多参数路径
```typescript
// 多个参数的情况
USER_ROLE: (userId: number, roleId: number) => `/users/${userId}/roles/${roleId}`,
```

### 3.3 复杂路径
```typescript
// 复杂路径示例
EXPORT_REPORT: (type: string, startDate: string, endDate: string) => 
  `/reports/export?type=${type}&start=${startDate}&end=${endDate}`,
```

---

## 4. 使用示例

### 4.1 在API文件中使用

```typescript
// src/api/user.ts
import { USER_API } from '@/constants/api';
import type { UserRecord, UserQueryParams } from '@/types';

export class UserApi {
  // 静态路径
  static async getUserList(): Promise<UserRecord[]> {
    const response = await request.get<UserRecord[]>(USER_API.LIST);
    return response.data;
  }

  // 动态路径
  static async getUserById(id: number): Promise<UserRecord> {
    const response = await request.get<UserRecord>(USER_API.DETAIL(id));
    return response.data;
  }

  // 复杂参数
  static async updateUser(id: number, userData: Partial<UserRecord>): Promise<UserRecord> {
    const response = await request.put<UserRecord>(
      USER_API.UPDATE(id),
      userData
    );
    return response.data;
  }
}
```

### 4.2 在组件中使用（如需要）

```typescript
// 通常不建议在组件中直接使用API常量
// 但如果需要，可以这样使用：
import { USER_API } from '@/constants/api';

// 用于构建链接或其他用途
const userDetailUrl = USER_API.DETAIL(userId);
```

---

## 5. 最佳实践

### 5.1 版本管理
当API版本升级时，可以这样处理：

```typescript
// v1 API
export const USER_API_V1 = {
  LIST: '/v1/users',
  // ...
} as const;

// v2 API
export const USER_API_V2 = {
  LIST: '/v2/users',
  // ...
} as const;

// 当前使用的版本
export const USER_API = USER_API_V2;
```

### 5.2 环境区分
```typescript
// 根据环境配置API前缀
const API_PREFIX = process.env.NODE_ENV === 'development' ? '/dev-api' : '/api';

export const USER_API = {
  LIST: `${API_PREFIX}/users`,
  // ...
} as const;
```

### 5.3 常量验证
添加运行时验证确保常量正确性：

```typescript
// 开发环境下验证API路径格式
if (process.env.NODE_ENV === 'development') {
  const validateApiPaths = (apiObject: Record<string, any>) => {
    Object.entries(apiObject).forEach(([key, value]) => {
      if (typeof value === 'string' && !value.startsWith('/')) {
        console.warn(`API路径格式错误: ${key} = ${value}`);
      }
    });
  };

  validateApiPaths(USER_API);
  validateApiPaths(AUTH_API);
}
```

### 5.4 类型安全
确保常量的类型安全：

```typescript
// 导出类型以供其他地方使用
export type UserApiKeys = keyof typeof USER_API;
export type AuthApiKeys = keyof typeof AUTH_API;

// 创建API路径联合类型
export type ApiPath = 
  | typeof USER_API[keyof typeof USER_API]
  | typeof AUTH_API[keyof typeof AUTH_API];
```

---

## 🔍 检查清单

在添加新的API常量时，请确认：

- [ ] 是否按照模块正确分组？
- [ ] 命名是否遵循规范？
- [ ] 是否添加了JSDoc注释？
- [ ] 动态路径是否使用函数形式？
- [ ] 是否使用了 `as const` 断言？
- [ ] 是否在 `API_ENDPOINTS` 中导出？

---

**版本信息**：v1.0 | **更新时间**：2025-06-27
