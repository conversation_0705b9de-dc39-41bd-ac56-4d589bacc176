# 认证状态持久化测试指南

## 🧪 测试步骤

### 1. 基础登录测试
1. 打开浏览器访问 `http://localhost:3001`
2. 如果未登录，会自动跳转到登录页面
3. 输入用户名和密码进行登录
4. 登录成功后，检查：
   - 页面跳转到主页面
   - 用户信息正确显示
   - 菜单正确加载

### 2. 持久化验证测试
1. 登录成功后，打开浏览器开发者工具
2. 查看 `Application` -> `Local Storage` -> `http://localhost:3001`
3. 应该能看到 `auth-store` 键，包含以下数据：
   ```json
   {
     "loginStatus": "LOGGED_IN",
     "userInfo": { ... },
     "accessToken": "...",
     "userMenus": [...],
     "userPermissions": [...],
     "userRoles": [...]
   }
   ```

### 3. 页面刷新测试 ⭐ **核心测试**
1. 在已登录状态下，按 `F5` 或 `Ctrl+R` 刷新页面
2. **预期结果**：
   - ✅ 页面不会跳转到登录页
   - ✅ 用户信息保持显示
   - ✅ 菜单状态保持正常
   - ✅ 不需要重新登录

### 4. 多标签页同步测试
1. 在当前标签页保持登录状态
2. 打开新标签页访问 `http://localhost:3001`
3. **预期结果**：
   - ✅ 新标签页直接显示主页面（不需要登录）
   - ✅ 用户状态与原标签页一致

### 5. 登出测试
1. 点击登出按钮
2. **预期结果**：
   - ✅ 跳转到登录页面
   - ✅ localStorage 中的 `auth-store` 数据被清除
   - ✅ 刷新页面仍然在登录页面

### 6. Token 过期处理测试
1. 登录后，手动修改 localStorage 中的 `accessToken` 为无效值
2. 刷新页面
3. **预期结果**：
   - ✅ 系统检测到 token 无效
   - ✅ 自动清除认证状态
   - ✅ 跳转到登录页面

## 🔍 调试信息

### 控制台日志
在浏览器控制台中，你应该能看到以下日志：
- 应用启动时的认证状态初始化日志
- Token 验证的结果
- 菜单加载的状态

### 网络请求
在 Network 标签中，关注以下请求：
- `/api/auth/login` - 登录请求
- `/api/auth/validate` - Token 验证请求
- `/api/auth/menus` - 菜单获取请求

## 🚨 常见问题排查

### 问题1：刷新后跳转到登录页
**可能原因**：
- pinia-plugin-persist 插件未正确配置
- localStorage 数据格式不正确
- Token 验证失败

**排查步骤**：
1. 检查 localStorage 中是否有 `auth-store` 数据
2. 检查控制台是否有错误信息
3. 检查 Token 验证请求是否成功

### 问题2：用户信息丢失
**可能原因**：
- 持久化配置中未包含 `userInfo` 字段
- 数据序列化/反序列化问题

**排查步骤**：
1. 检查 store 配置中的 `paths` 数组
2. 验证 localStorage 中的数据完整性

### 问题3：菜单不显示
**可能原因**：
- 菜单数据未正确持久化
- 菜单组件未正确读取状态

**排查步骤**：
1. 检查 `userMenus` 是否在持久化配置中
2. 检查菜单组件的数据绑定

## 📊 性能监控

### 初始化时间
- 应用启动到认证状态恢复的时间应该很短（< 100ms）
- 避免不必要的 API 调用

### 存储大小
- 监控 localStorage 中认证数据的大小
- 避免存储过大的菜单或权限数据

## ✅ 测试检查清单

- [ ] 正常登录流程工作正常
- [ ] localStorage 中正确保存认证数据
- [ ] 页面刷新后保持登录状态
- [ ] 多标签页状态同步
- [ ] 登出后正确清除数据
- [ ] Token 过期后正确处理
- [ ] 控制台无错误信息
- [ ] 网络请求正常

## 🎯 预期改进效果

**修复前**：
```
登录 → 刷新页面 → 跳转到登录页 ❌
```

**修复后**：
```
登录 → 刷新页面 → 保持登录状态 ✅
```

这个修复应该完全解决您提到的"刷新页面后跳转判定未登录"的问题！
