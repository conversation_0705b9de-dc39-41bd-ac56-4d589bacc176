{"component": "MainLayout.vue", "version": "1.0.0", "framework": "Vue 3 + TypeScript", "ui_library": "Ant Design Vue 4.x", "theme_system": "CSS Variables + ThemeProvider", "architecture": {"pattern": "Sidebar + Main Content Layout", "responsive": true, "mobile_first": true, "theme_integration": "complete"}, "layout_structure": {"root": {"component": "a-layout", "class": "main-layout", "attributes": ["has-sider"], "min_height": "100vh"}, "sidebar": {"component": "a-layout-sider", "class": "layout-sider", "width": {"expanded": "220px", "collapsed": "80px"}, "position": "fixed", "z_index": 10, "border_radius": "0 16px 16px 0", "children": ["logo-container", "side-menu"]}, "main_content": {"component": "a-layout", "class": "main-content-layout", "margin_left": {"desktop": "220px", "mobile": "0"}, "children": ["header", "content", "footer"]}, "mobile_overlay": {"class": "mobile-overlay", "display": "none", "media_query": "@media (max-width: 992px)", "z_index": 9}}, "components": {"logo_container": {"class": "logo-container", "height": "64px", "padding": "16px 24px", "margin": "12px", "border_radius": "12px", "background": "linear-gradient(135deg, var(--theme-primary-bg) 0%, var(--theme-primary-border) 50%, var(--theme-bg-elevated) 100%)", "features": {"shine_effect": {"duration": "0.6s", "direction": "left to right", "trigger": "hover"}, "hover_effect": {"transform": "translateY(-2px)", "box_shadow": "0 8px 25px var(--theme-primary-bg)"}}, "children": {"logo_icon": {"size": "28px", "color": "var(--theme-primary)", "hover_transform": "scale(1.05)"}, "logo_text": {"font_size": "18px", "font_weight": "600", "background": "linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-info) 100%)", "text_fill": "transparent"}}}, "side_menu": {"class": "side-menu", "component": "a-menu", "theme": "light", "mode": "inline", "padding": "8px 12px", "menu_items": {"style": {"margin": "6px 0", "border_radius": "12px", "transition": "all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)"}, "hover_effect": {"background": "var(--theme-bg-elevated)", "transform": "translateX(4px)", "box_shadow": "0 4px 12px var(--theme-primary-bg)"}, "selected_state": {"background": "var(--theme-primary-bg)", "color": "var(--theme-primary)", "indicator": {"width": "4px", "height": "60%", "background": "var(--theme-primary)", "position": "left", "border_radius": "0 4px 4px 0"}}}}, "header": {"component": "a-layout-header", "class": "layout-header", "height": "64px", "background": "var(--theme-bg-container)", "box_shadow": "0 4px 20px var(--theme-fill-quaternary)", "position": "sticky", "z_index": 9, "sections": {"left": {"class": "header-left", "children": ["trigger-btn", "breadcrumb"]}, "right": {"class": "header-right", "children": ["search-box", "notification", "theme-toggle", "user-info"]}}}, "search_box": {"component": "a-input-search", "height": "36px", "border_radius": "10px", "placeholder": "搜索功能、页面、数据...", "button": {"background": "var(--theme-primary)", "hover_background": "var(--theme-primary-hover)", "color": "#ffffff"}, "responsive": {"display": "none", "media_query": "@media (max-width: 576px)"}}, "content_wrapper": {"class": "content-wrapper", "background": "var(--theme-bg-container)", "border_radius": "20px", "padding": "32px", "box_shadow": "0 8px 32px var(--theme-fill-quaternary)", "decoration": {"top_bar": {"height": "4px", "background": "linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-success) 100%)", "border_radius": "20px 20px 0 0"}}, "hover_effect": {"box_shadow": "0 12px 40px var(--theme-fill-quaternary)", "transform": "translateY(-2px)"}}}, "responsive_breakpoints": {"large": "≥992px", "medium": "768px-991px", "small": "≤767px", "extra_small": "≤576px"}, "responsive_behavior": {"desktop": {"sidebar": "fixed", "content_margin": "220px"}, "tablet": {"sidebar": "collapsible", "content_margin": "dynamic"}, "mobile": {"sidebar": "drawer", "content_margin": "0", "overlay": "visible"}}, "theme_variables": {"backgrounds": ["--theme-bg-container", "--theme-bg-elevated", "--theme-bg-layout"], "colors": ["--theme-primary", "--theme-primary-bg", "--theme-primary-border", "--theme-primary-hover", "--theme-success", "--theme-info"], "text": ["--theme-text-base", "--theme-text-secondary", "--theme-text-tertiary", "--theme-text-quaternary"], "fills": ["--theme-fill-quaternary"]}, "animations": {"easing": "cubic-bezier(0.25, 0.46, 0.45, 0.94)", "durations": {"fast": "0.3s", "standard": "0.4s", "slow": "0.6s"}, "effects": {"hover_lift": "translateY(-2px)", "scale_up": "scale(1.05)", "slide_right": "translateX(4px)"}, "transitions": {"fade": {"enter": "opacity: 0 → 1", "leave": "opacity: 1 → 0"}, "fade_slide": {"enter": "opacity: 0, translateX(-20px) → opacity: 1, translateX(0)", "leave": "opacity: 1, translateX(0) → opacity: 0, translateX(20px)"}}}, "state_management": {"reactive_refs": {"collapsed": "boolean", "selectedKeys": "string[]", "openKeys": "string[]", "searchText": "string", "isDark": "boolean", "isMobile": "boolean", "mobileMenuVisible": "boolean"}, "methods": {"toggleCollapsed": "切换侧边栏折叠状态", "toggleTheme": "切换主题模式", "handleSearch": "处理搜索逻辑", "onBreakpoint": "响应式断点处理"}}, "accessibility": {"keyboard_navigation": true, "screen_reader_support": true, "focus_management": true, "color_contrast": "WCAG AA compliant"}, "performance": {"optimizations": ["GPU加速动画", "组件懒加载", "防抖搜索", "条件渲染"], "bundle_size": "optimized", "render_performance": "60fps"}, "design_tokens": {"spacing": {"xs": "8px", "sm": "12px", "md": "16px", "lg": "24px", "xl": "32px"}, "border_radius": {"small": "8px", "medium": "12px", "large": "16px", "xlarge": "20px"}, "shadows": {"light": "0 2px 8px var(--theme-fill-quaternary)", "medium": "0 4px 12px var(--theme-primary-bg)", "heavy": "0 8px 32px var(--theme-fill-quaternary)"}, "typography": {"font_family": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "font_sizes": {"small": "13px", "medium": "14px", "large": "16px", "xlarge": "18px", "xxlarge": "28px"}, "font_weights": {"normal": "400", "medium": "500", "semibold": "600"}}}, "menu_structure": {"items": [{"key": "dashboard", "icon": "DashboardOutlined", "title": "工作台", "type": "item"}, {"key": "financial", "icon": "WalletOutlined", "title": "财务管理", "type": "submenu", "children": [{"key": "transactions", "title": "交易记录"}, {"key": "reports", "title": "财务报表"}]}, {"key": "analytics", "icon": "LineChartOutlined", "title": "数据分析", "type": "item"}, {"key": "users", "icon": "TeamOutlined", "title": "用户管理", "type": "item"}, {"key": "settings", "icon": "SettingOutlined", "title": "系统设置", "type": "item"}]}, "user_dropdown_menu": {"items": [{"key": "profile", "icon": "UserOutlined", "title": "个人中心"}, {"key": "settings", "icon": "SettingOutlined", "title": "账号设置"}, {"type": "divider"}, {"key": "logout", "icon": "LogoutOutlined", "title": "退出登录"}]}, "css_classes": {"layout": ["main-layout", "layout-sider", "main-content-layout", "layout-header", "layout-content", "layout-footer"], "components": ["logo-container", "side-menu", "header-content", "header-left", "header-right", "content-wrapper", "footer-content"], "interactive": ["trigger-btn", "header-btn", "search-button", "user-info", "notification-badge"], "responsive": ["mobile-overlay", "mobile-visible", "breadcrumb"]}, "implementation_notes": {"vue_features": ["Composition API", "TypeScript支持", "响应式状态管理", "路由集成", "组件通信"], "antd_components": ["a-layout", "a-layout-sider", "a-layout-header", "a-layout-content", "a-layout-footer", "a-menu", "a-button", "a-input-search", "a-avatar", "a-dropdown", "a-badge", "a-breadcrumb"], "custom_features": ["主题系统集成", "响应式断点处理", "移动端抽屉菜单", "光泽动画效果", "渐变背景和文字"]}}