# 动态菜单系统文档

## 概述

本项目实现了一个完整的动态菜单系统，支持从后端获取菜单数据并自动渲染为 Ant Design Vue 菜单组件。

## 核心功能

### 1. 菜单数据类型定义

#### MenuRecord (后端菜单数据格式)
```typescript
interface MenuRecord {
  id: ID;                    // 菜单ID
  parentId: ID;             // 父菜单ID
  name: string;             // 菜单名称
  title: string;            // 菜单标题
  type: number;             // 菜单类型：1-菜单 2-按钮 3-目录
  path: string;             // 路由路径
  component: string;        // 组件路径
  icon: string;             // 图标名称
  children: MenuRecord[];   // 子菜单
}
```

#### AntdMenuItemType (前端菜单数据格式)
```typescript
interface AntdMenuItem {
  key: string;              // 菜单键
  label?: string;           // 菜单标签
  icon?: any;               // 图标组件
  children?: AntdMenuItemType[]; // 子菜单
  disabled?: boolean;       // 是否禁用
}
```

### 2. 菜单转换工具

#### transformMenusToAntd()
将后端 MenuRecord 格式转换为 Ant Design Vue 菜单格式：

```typescript
import { transformMenusToAntd } from '@/utils/menu';

const antdMenuItems = transformMenusToAntd(menuRecords, {
  showIcon: true,           // 是否显示图标
  filterHidden: true,       // 是否过滤隐藏菜单
  filterButtons: true,      // 是否过滤按钮类型菜单
  iconMapper: getIconComponent, // 图标映射函数
  labelGenerator: (menu) => menu.title, // 标签生成函数
});
```

#### 图标映射
系统内置了丰富的图标映射，支持字符串到 Vue 图标组件的自动转换：

```typescript
// 支持的图标格式
'dashboard' -> DashboardOutlined
'user' -> UserOutlined
'setting' -> SettingOutlined
// ... 更多图标
```

### 3. 菜单管理组合函数

#### useMenu()
提供完整的菜单状态管理和操作方法：

```typescript
const {
  // 状态
  selectedKeys,           // 当前选中的菜单键
  openKeys,              // 当前展开的菜单键
  menuItems,             // 转换后的菜单数据
  menuLoading,           // 菜单加载状态
  
  // 计算属性
  rawMenus,              // 原始菜单数据
  allMenuKeys,           // 所有菜单键
  currentMenuKey,        // 当前路由对应的菜单键
  currentParentKeys,     // 当前菜单的父级键
  
  // 方法
  handleMenuClick,       // 菜单点击处理
  handleOpenChange,      // 菜单展开/收起处理
  refreshMenu,           // 刷新菜单数据
  resetMenuState,        // 重置菜单状态
  getBreadcrumb,         // 获取面包屑数据
} = useMenu();
```

### 4. 在组件中使用

#### 基本用法
```vue
<template>
  <a-menu
    v-model:selectedKeys="selectedKeys"
    v-model:openKeys="openKeys"
    mode="inline"
    :items="menuItems"
    @click="handleMenuClick"
    @openChange="handleOpenChange"
  />
</template>

<script setup lang="ts">
import { useMenu } from '@/composables/useMenu';

const {
  selectedKeys,
  openKeys,
  menuItems,
  handleMenuClick,
  handleOpenChange,
} = useMenu();
</script>
```

#### 在 MainLayout 中的应用
MainLayout.vue 已经集成了动态菜单系统，会自动：
- 从认证状态中获取用户菜单
- 转换为 Ant Design 格式
- 根据当前路由自动选中和展开菜单
- 处理菜单点击和路由跳转

### 5. 菜单数据流

```
1. 用户登录 -> AuthApi.getUserMenus()
2. 获取 MenuRecord[] -> 存储到 authStore.userMenus
3. useMenu() -> transformMenusToAntd() -> AntdMenuItemType[]
4. 渲染到 <a-menu :items="menuItems" />
5. 用户点击菜单 -> handleMenuClick() -> 路由跳转
6. 路由变化 -> 自动更新菜单选中状态
```

### 6. 测试和调试

#### 访问测试页面
```
http://localhost:3001/menu-test
```

测试页面提供：
- 菜单预览
- 加载模拟数据
- 菜单状态查看
- 面包屑导航
- 详细信息展示

#### 模拟数据
```typescript
import { getMockMenuData } from '@/mock/menu';

// 获取测试用的菜单数据
const mockMenus = getMockMenuData();
```

### 7. 扩展和自定义

#### 自定义图标映射
```typescript
const customIconMapper = (iconName: string) => {
  // 自定义图标映射逻辑
  return MyCustomIcon;
};

const menuItems = transformMenusToAntd(menus, {
  iconMapper: customIconMapper,
});
```

#### 自定义标签生成
```typescript
const customLabelGenerator = (menu: MenuRecord) => {
  // 自定义标签生成逻辑
  return `${menu.title} (${menu.name})`;
};

const menuItems = transformMenusToAntd(menus, {
  labelGenerator: customLabelGenerator,
});
```

### 8. 最佳实践

1. **菜单数据缓存**: 菜单数据会自动缓存到 localStorage
2. **权限控制**: 通过 `filterHidden` 和 `filterButtons` 控制菜单显示
3. **图标统一**: 使用统一的图标命名规范
4. **路由同步**: 菜单状态与路由自动同步
5. **错误处理**: 完善的错误处理和用户提示

### 9. 故障排除

#### 菜单不显示
- 检查 `authStore.userMenus` 是否有数据
- 检查菜单数据格式是否正确
- 检查是否被过滤器过滤掉

#### 图标不显示
- 检查图标名称是否在映射表中
- 检查图标组件是否正确导入
- 使用默认图标作为后备方案

#### 路由跳转失败
- 检查菜单的 `path` 字段是否正确
- 检查路由是否已注册
- 检查路由权限配置

## 总结

本动态菜单系统提供了完整的菜单管理解决方案，支持：
- 类型安全的菜单数据转换
- 丰富的图标映射
- 自动的状态管理
- 灵活的配置选项
- 完善的测试工具

通过简单的 `:items="menuItems"` 即可实现复杂的动态菜单功能。
