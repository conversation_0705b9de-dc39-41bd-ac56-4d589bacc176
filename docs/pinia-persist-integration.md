# Pinia 持久化插件集成

## 概述

已成功集成 `pinia-plugin-persistedstate` 插件来管理认证状态的持久化，替代了之前的手动缓存管理方式。

## ⚠️ 重要修复

**问题发现**: 最初使用的 `pinia-plugin-persist` (v1.0.0) 是一个过时的插件，已经3年没有更新，导致持久化功能无法正常工作。

**解决方案**: 切换到活跃维护的 `pinia-plugin-persistedstate` (v4.3.0)，这是目前推荐的 Pinia 持久化插件。

## 安装

```bash
# 移除旧插件（如果已安装）
pnpm remove pinia-plugin-persist

# 安装新插件
pnpm add pinia-plugin-persistedstate
```

## 主要改进

### 1. 插件配置

**src/stores/index.ts**
```typescript
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
```

### 2. Store 持久化配置

**src/stores/modules/auth.ts**
```typescript
export const useAuthStore = defineStore('auth', () => {
  // ... store 逻辑
}, {
  // 持久化配置
  persist: {
    key: 'auth-store',
    storage: localStorage,
    pick: [
      'loginStatus',
      'userInfo',
      'accessToken',
      'userMenus',
      'userPermissions',
      'userRoles'
    ]
  }
});
```

### 3. 简化的数据管理

#### 删除的手动缓存代码
- 移除了 `AuthStorageManager` 的导入和使用
- 简化了 `setAuthData` 方法，删除手动保存到 localStorage 的代码
- 简化了 `clearAuthData` 方法，删除手动清除 localStorage 的代码

#### 简化的 `initAuth` 方法
```typescript
const initAuth = async (): Promise<void> => {
  try {
    // 由于使用了 pinia-plugin-persist，状态已经自动从 localStorage 恢复
    // 只需要检查是否有有效的认证状态
    
    if (accessToken.value && userInfo.value) {
      // 如果有 token 和用户信息，验证 token 有效性
      const isValid = await validateToken();
      if (isValid) {
        loginStatus.value = LoginStatus.LOGGED_IN;
        
        // 如果没有菜单数据，重新获取
        if (userMenus.value.length === 0) {
          await getUserMenus();
        }
      } else {
        clearAuthData();
        loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      }
    } else {
      // 检查是否有独立存储的 token
      const token = TokenManager.getToken();
      if (token && token !== accessToken.value) {
        accessToken.value = token;
        const isValid = await validateToken();
        if (isValid) {
          await Promise.all([getCurrentUser(), getUserMenus()]);
          loginStatus.value = LoginStatus.LOGGED_IN;
        } else {
          clearAuthData();
          loginStatus.value = LoginStatus.NOT_LOGGED_IN;
        }
      } else {
        loginStatus.value = LoginStatus.NOT_LOGGED_IN;
      }
    }
  } catch (error) {
    console.error('初始化认证状态失败:', error);
    clearAuthData();
    loginStatus.value = LoginStatus.NOT_LOGGED_IN;
  } finally {
    isInitialized.value = true;
  }
};
```

## 新的认证流程

### 登录流程
1. 用户输入凭据并提交
2. 调用 `login` 方法
3. API 返回认证数据
4. 调用 `setAuthData` 设置状态
5. **pinia-plugin-persist 自动将状态保存到 localStorage**

### 页面刷新流程
1. 页面刷新，Vue 应用重新初始化
2. **pinia-plugin-persist 自动从 localStorage 恢复状态**
3. 应用启动时调用 `initAuth`
4. 检查恢复的状态是否有效
5. 如果有效，验证 token 并设置登录状态
6. 如果无效，清除状态并设置为未登录

### 登出流程
1. 调用 `logout` 方法
2. 调用 API 进行服务端登出
3. 调用 `clearAuthData` 清除状态
4. **pinia-plugin-persist 自动清除 localStorage 中的数据**

## 优势

### 1. 自动化管理
- 无需手动管理 localStorage 的读写
- 状态变化自动同步到存储
- 应用启动时自动恢复状态

### 2. 代码简化
- 删除了大量手动缓存管理代码
- 减少了出错的可能性
- 提高了代码的可维护性

### 3. 配置灵活
- 可以选择性持久化特定状态
- 支持自定义存储键名
- 支持不同的存储方式（localStorage, sessionStorage 等）

### 4. 类型安全
- 插件提供完整的 TypeScript 支持
- 编译时检查配置的正确性

## 测试验证

### 测试场景
1. **正常登录** - 验证登录后状态正确保存
2. **页面刷新** - 验证刷新后状态正确恢复
3. **Token 过期** - 验证过期 token 的处理
4. **手动清除缓存** - 验证清除后的行为
5. **多标签页同步** - 验证不同标签页间的状态同步

### 验证步骤
1. 登录系统，检查 localStorage 中的 `auth-store` 数据
2. 刷新页面，确认不会跳转到登录页
3. 检查用户信息和菜单是否正确显示
4. 登出后检查 localStorage 数据是否清除

## 注意事项

1. **兼容性**: 确保目标浏览器支持 localStorage
2. **安全性**: 敏感信息（如 token）仍需要适当的安全措施
3. **存储限制**: localStorage 有大小限制，避免存储过大的数据
4. **同步问题**: 多标签页间的状态同步由插件自动处理

这个集成大大简化了认证状态的管理，提供了更可靠和易维护的解决方案。
