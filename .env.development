# 开发环境配置
VITE_APP_ENV=development

# 应用标题
VITE_APP_TITLE=Combo-OPM 管理系统

# API 配置
VITE_API_BASE_URL=/api
VITE_BACKEND_HOST=localhost
VITE_BACKEND_PORT=1001

# 开发功能开关
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK=false
VITE_SHOW_DEV_TOOLS=true

# 应用端口
VITE_PORT=3001

# 是否开启代理
VITE_USE_PROXY=true

# 代理目标地址
VITE_PROXY_TARGET=http://localhost:1001

# 请求超时时间 (ms)
VITE_REQUEST_TIMEOUT=10000

# Token存储key
VITE_TOKEN_KEY=combo-opm-token

# 上传文件大小限制 (MB)
VITE_UPLOAD_SIZE_LIMIT=10

# 是否开启路由缓存
VITE_USE_ROUTE_CACHE=true

# 是否开启页面切换动画
VITE_USE_PAGE_TRANSITION=true
