# 医美CRM管理系统

专业的医美机构客户管理平台，基于Vue 3 + TypeScript + Ant Design Vue开发。

## 功能特性

- 🎨 **现代化UI设计** - 基于Ant Design Vue组件库，界面美观专业
- 📱 **多种登录方式** - 支持手机登录、微信登录、管理员登录
- 🔐 **安全认证** - 完善的用户认证和权限管理
- 💼 **企业级标准** - 遵循企业级开发规范和最佳实践

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Ant Design Vue 4.x
- **图标库**: @ant-design/icons-vue
- **包管理器**: pnpm

## 开发环境

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 预览生产版本
pnpm preview
```

## 项目结构

```
src/
├── views/          # 页面组件
│   └── Login.vue   # 登录页面
├── components/     # 公共组件
├── assets/         # 静态资源
├── App.vue         # 根组件
├── main.ts         # 入口文件
└── style.css       # 全局样式
```

## 登录页面功能

### 布局设计
- 左右分割式布局
- 左侧：公司Logo和品牌信息展示
- 右侧：登录表单区域

### 登录方式
1. **手机登录** - 手机号码 + 验证码登录
2. **微信登录** - 扫码登录（模态框形式）
3. **管理员登录** - 管理员专用登录入口

### 特色功能
- 响应式设计，适配各种屏幕尺寸
- 优雅的渐变背景和动画效果
- 完整的表单验证和错误提示
- 微信登录二维码模态框

## 开发规范

- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API规范
- 优先使用Ant Design组件，减少自定义样式
- 代码格式化和ESLint规范检查

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
