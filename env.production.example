# ============================================
# 生产环境配置
# 将此文件重命名为 .env.production
# ============================================

# 应用配置
VITE_APP_TITLE=shadcn-vue-admin
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于 Vue 3 + shadcn-vue 的现代化管理系统

# API配置
VITE_API_BASE_URL=https://api.yourserver.com/api
VITE_API_TIMEOUT=10000

# 后端服务配置
VITE_BACKEND_HOST=api.yourserver.com
VITE_BACKEND_PORT=443

# 开发环境配置
VITE_DEV_TOOLS=false

# 日志控制配置（生产环境应该关闭调试日志）
VITE_DEBUG=false
VITE_LOG_LEVEL=error

# 其他配置
VITE_PUBLIC_PATH=/

# 应用基础配置
VITE_APP_ENV=production

# 开发服务器配置 (生产环境构建不使用，但保留配置)
VITE_DEV_SERVER_PORT=3000

# 认证配置 (生产环境不应包含默认token)
VITE_API_TOKEN_KEY=combo-opm-token
# VITE_DEFAULT_TOKEN= # 生产环境不设置默认token

# 功能开关 - 生产环境关闭调试功能
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK=false
VITE_SHOW_DEV_TOOLS=false
VITE_ENABLE_PERFORMANCE_MONITOR=true
VITE_ENABLE_ERROR_TRACKING=true

# 日志配置
VITE_ENABLE_CONSOLE_LOG=false
VITE_ENABLE_REMOTE_LOG=true 